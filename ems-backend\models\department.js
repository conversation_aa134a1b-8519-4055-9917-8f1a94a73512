import pool from '../db.js';

// Create a new department
export async function createDepartment(departmentData) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const result = await client.query(`
      INSERT INTO departments (
        name, description, head_of_department, cost_center, budget_allocated
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [
      departmentData.name,
      departmentData.description,
      departmentData.headOfDepartment,
      departmentData.costCenter,
      departmentData.budgetAllocated
    ]);

    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Get all departments
export async function getAllDepartments(options = {}) {
  const { includeInactive = false } = options;
  
  let whereClause = '';
  const queryParams = [];
  
  if (!includeInactive) {
    whereClause = 'WHERE d.is_active = true';
  }

  const query = `
    SELECT 
      d.*,
      e.employee_id as head_employee_id,
      CONCAT(
        CASE WHEN e.first_name_encrypted IS NOT NULL 
        THEN 'Head: ' || e.employee_id 
        ELSE 'No Head Assigned' END
      ) as head_info,
      COUNT(emp.id) as employee_count
    FROM departments d
    LEFT JOIN employees e ON d.head_of_department = e.id
    LEFT JOIN employees emp ON emp.department_id = d.id AND emp.status = 'active'
    ${whereClause}
    GROUP BY d.id, e.employee_id, e.first_name_encrypted
    ORDER BY d.name
  `;

  const result = await pool.query(query, queryParams);
  return result.rows;
}

// Get department by ID
export async function getDepartmentById(id) {
  const query = `
    SELECT 
      d.*,
      e.employee_id as head_employee_id,
      COUNT(emp.id) as employee_count,
      SUM(CASE WHEN emp.status = 'active' THEN 1 ELSE 0 END) as active_employee_count
    FROM departments d
    LEFT JOIN employees e ON d.head_of_department = e.id
    LEFT JOIN employees emp ON emp.department_id = d.id
    WHERE d.id = $1
    GROUP BY d.id, e.employee_id
  `;
  
  const result = await pool.query(query, [id]);
  
  if (result.rows.length === 0) {
    return null;
  }

  return result.rows[0];
}

// Update department
export async function updateDepartment(id, updateData) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Build update query dynamically
    const updateFields = [];
    const values = [];
    let paramCount = 0;

    const allowedFields = ['name', 'description', 'head_of_department', 'cost_center', 'budget_allocated', 'is_active'];
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        paramCount++;
        updateFields.push(`${field} = $${paramCount}`);
        values.push(updateData[field]);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    // Add updated_at
    paramCount++;
    updateFields.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add department ID as the last parameter
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE departments 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await client.query(updateQuery, values);
    
    if (result.rows.length === 0) {
      throw new Error('Department not found');
    }

    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Delete department (soft delete)
export async function deleteDepartment(id) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Check if department has active employees
    const employeeCheck = await client.query(`
      SELECT COUNT(*) as count 
      FROM employees 
      WHERE department_id = $1 AND status = 'active'
    `, [id]);

    if (parseInt(employeeCheck.rows[0].count) > 0) {
      throw new Error('Cannot delete department with active employees');
    }

    const result = await client.query(`
      UPDATE departments 
      SET is_active = false, updated_at = NOW()
      WHERE id = $1 
      RETURNING *
    `, [id]);
    
    if (result.rows.length === 0) {
      throw new Error('Department not found');
    }

    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Get department statistics
export async function getDepartmentStatistics() {
  const query = `
    SELECT 
      COUNT(*) as total_departments,
      COUNT(CASE WHEN is_active = true THEN 1 END) as active_departments,
      COUNT(CASE WHEN head_of_department IS NOT NULL THEN 1 END) as departments_with_head,
      AVG(budget_allocated) as average_budget,
      SUM(budget_allocated) as total_budget
    FROM departments
  `;
  
  const result = await pool.query(query);
  return result.rows[0];
}

// Get employees in a department
export async function getDepartmentEmployees(departmentId, options = {}) {
  const { 
    page = 1, 
    limit = 50, 
    status = 'active',
    sortBy = 'hire_date',
    sortOrder = 'DESC' 
  } = options;

  const offset = (page - 1) * limit;
  let whereClause = 'WHERE e.department_id = $1';
  const queryParams = [departmentId];
  let paramCount = 1;

  if (status) {
    paramCount++;
    whereClause += ` AND e.status = $${paramCount}`;
    queryParams.push(status);
  }

  const query = `
    SELECT 
      e.id, e.employee_id, e.email, e.job_title, e.employment_type,
      e.work_location, e.hire_date, e.status, e.created_at,
      COUNT(*) OVER() as total_count
    FROM employees e
    ${whereClause}
    ORDER BY e.${sortBy} ${sortOrder}
    LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
  `;

  queryParams.push(limit, offset);
  const result = await pool.query(query, queryParams);
  
  return {
    employees: result.rows,
    totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
    page,
    limit,
    totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0
  };
}

// Get department hierarchy
export async function getDepartmentHierarchy() {
  const query = `
    WITH RECURSIVE dept_hierarchy AS (
      -- Base case: departments without parent (assuming we add parent_id later)
      SELECT 
        id, name, description, head_of_department, 
        cost_center, budget_allocated, 0 as level,
        ARRAY[name] as path
      FROM departments 
      WHERE is_active = true
      
      UNION ALL
      
      -- Recursive case would go here if we had parent-child relationships
      SELECT 
        d.id, d.name, d.description, d.head_of_department,
        d.cost_center, d.budget_allocated, 0 as level,
        ARRAY[d.name] as path
      FROM departments d
      WHERE false -- Placeholder for recursive logic
    )
    SELECT * FROM dept_hierarchy
    ORDER BY level, name
  `;
  
  const result = await pool.query(query);
  return result.rows;
}
