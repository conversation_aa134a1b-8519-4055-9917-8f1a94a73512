import pool from '../db.js';
import { encrypt, decrypt } from '../utils/encrypt.js';

// Create a new employee with encrypted PII
export async function createEmployee(employeeData) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Generate employee ID
    const employeeIdResult = await client.query(
      'SELECT COALESCE(MAX(CAST(SUBSTRING(employee_id FROM 4) AS INTEGER)), 0) + 1 as next_id FROM employees WHERE employee_id LIKE \'EMP%\''
    );
    const nextId = employeeIdResult.rows[0].next_id;
    const employeeId = `EMP${nextId.toString().padStart(4, '0')}`;

    // Encrypt sensitive data
    const encryptedData = {
      first_name_encrypted: encrypt(employeeData.firstName),
      last_name_encrypted: encrypt(employeeData.lastName),
      phone_encrypted: employeeData.phone ? encrypt(employeeData.phone) : null,
      date_of_birth_encrypted: employeeData.dateOfBirth ? encrypt(employeeData.dateOfBirth) : null,
      national_id_encrypted: employeeData.nationalId ? encrypt(employeeData.nationalId) : null,
      salary_encrypted: employeeData.salary ? encrypt(employeeData.salary.toString()) : null,
      emergency_contact_encrypted: employeeData.emergencyContact ? encrypt(JSON.stringify(employeeData.emergencyContact)) : null
    };

    // Insert employee record
    const employeeResult = await client.query(`
      INSERT INTO employees (
        employee_id, email, first_name_encrypted, last_name_encrypted,
        phone_encrypted, date_of_birth_encrypted, national_id_encrypted,
        hire_date, status, department_id, manager_id, job_title,
        employment_type, work_location, salary_encrypted, currency,
        bio, skills, certifications, emergency_contact_encrypted,
        created_by, updated_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22)
      RETURNING id, employee_id, email, hire_date, status, department_id, manager_id, job_title, employment_type, work_location, currency, bio, skills, certifications, created_at, updated_at
    `, [
      employeeId,
      employeeData.email,
      encryptedData.first_name_encrypted,
      encryptedData.last_name_encrypted,
      encryptedData.phone_encrypted,
      encryptedData.date_of_birth_encrypted,
      encryptedData.national_id_encrypted,
      employeeData.hireDate,
      employeeData.status || 'active',
      employeeData.departmentId,
      employeeData.managerId,
      employeeData.jobTitle,
      employeeData.employmentType,
      employeeData.workLocation,
      encryptedData.salary_encrypted,
      employeeData.currency || 'USD',
      employeeData.bio,
      employeeData.skills ? JSON.stringify(employeeData.skills) : null,
      employeeData.certifications ? JSON.stringify(employeeData.certifications) : null,
      encryptedData.emergency_contact_encrypted,
      employeeData.createdBy,
      employeeData.createdBy
    ]);

    const employee = employeeResult.rows[0];

    // Insert address if provided
    if (employeeData.address) {
      await client.query(`
        INSERT INTO employee_addresses (
          employee_id, address_type, street_address_encrypted, city_encrypted,
          state_province, postal_code, country_code, is_primary
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        employee.id,
        employeeData.address.type || 'home',
        encrypt(employeeData.address.streetAddress),
        encrypt(employeeData.address.city),
        employeeData.address.stateProvince,
        employeeData.address.postalCode,
        employeeData.address.countryCode,
        true
      ]);
    }

    // Create onboarding record
    await client.query(`
      INSERT INTO employee_onboarding (employee_id, status, start_date, expected_completion_date)
      VALUES ($1, $2, $3, $4)
    `, [
      employee.id,
      'pending',
      employeeData.hireDate,
      new Date(new Date(employeeData.hireDate).getTime() + 14 * 24 * 60 * 60 * 1000) // 14 days from hire date
    ]);

    await client.query('COMMIT');
    return employee;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Get all employees with decrypted data (for authorized users)
export async function getAllEmployees(options = {}) {
  const {
    page = 1,
    limit = 50,
    search = '',
    department = '',
    status = '',
    sortBy = 'created_at',
    sortOrder = 'DESC'
  } = options;

  const offset = (page - 1) * limit;
  let whereClause = 'WHERE 1=1';
  const queryParams = [];
  let paramCount = 0;

  if (search) {
    paramCount++;
    whereClause += ` AND (e.email ILIKE $${paramCount} OR e.job_title ILIKE $${paramCount})`;
    queryParams.push(`%${search}%`);
  }

  if (department) {
    paramCount++;
    whereClause += ` AND e.department_id = $${paramCount}`;
    queryParams.push(department);
  }

  if (status) {
    paramCount++;
    whereClause += ` AND e.status = $${paramCount}`;
    queryParams.push(status);
  }

  const query = `
    SELECT
      e.id, e.employee_id, e.email, e.first_name_encrypted, e.last_name_encrypted,
      e.phone_encrypted, e.hire_date, e.status, e.job_title, e.employment_type,
      e.work_location, e.currency, e.bio, e.skills, e.certifications,
      e.created_at, e.updated_at,
      d.name as department_name,
      m.employee_id as manager_employee_id,
      COUNT(*) OVER() as total_count
    FROM employees e
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN employees m ON e.manager_id = m.id
    ${whereClause}
    ORDER BY e.${sortBy} ${sortOrder}
    LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
  `;

  queryParams.push(limit, offset);
  const result = await pool.query(query, queryParams);

  // Decrypt sensitive data for authorized access
  const employees = result.rows.map(emp => ({
    ...emp,
    firstName: emp.first_name_encrypted ? decrypt(emp.first_name_encrypted) : null,
    lastName: emp.last_name_encrypted ? decrypt(emp.last_name_encrypted) : null,
    phone: emp.phone_encrypted ? decrypt(emp.phone_encrypted) : null,
    fullName: emp.first_name_encrypted && emp.last_name_encrypted
      ? `${decrypt(emp.first_name_encrypted)} ${decrypt(emp.last_name_encrypted)}`
      : null,
    // Remove encrypted fields from response
    first_name_encrypted: undefined,
    last_name_encrypted: undefined,
    phone_encrypted: undefined
  }));

  return {
    employees,
    totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
    page,
    limit,
    totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0
  };
}

// Get employee by ID with full details
export async function getEmployeeById(id) {
  const query = `
    SELECT
      e.*,
      d.name as department_name,
      m.employee_id as manager_employee_id,
      u.email as user_email
    FROM employees e
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN employees m ON e.manager_id = m.id
    LEFT JOIN users u ON e.user_id = u.id
    WHERE e.id = $1
  `;

  const result = await pool.query(query, [id]);
  if (result.rows.length === 0) {
    return null;
  }

  const employee = result.rows[0];

  // Decrypt sensitive data
  const decryptedEmployee = {
    ...employee,
    firstName: employee.first_name_encrypted ? decrypt(employee.first_name_encrypted) : null,
    lastName: employee.last_name_encrypted ? decrypt(employee.last_name_encrypted) : null,
    phone: employee.phone_encrypted ? decrypt(employee.phone_encrypted) : null,
    dateOfBirth: employee.date_of_birth_encrypted ? decrypt(employee.date_of_birth_encrypted) : null,
    nationalId: employee.national_id_encrypted ? decrypt(employee.national_id_encrypted) : null,
    salary: employee.salary_encrypted ? parseFloat(decrypt(employee.salary_encrypted)) : null,
    emergencyContact: employee.emergency_contact_encrypted ? JSON.parse(decrypt(employee.emergency_contact_encrypted)) : null,
    // Remove encrypted fields
    first_name_encrypted: undefined,
    last_name_encrypted: undefined,
    phone_encrypted: undefined,
    date_of_birth_encrypted: undefined,
    national_id_encrypted: undefined,
    salary_encrypted: undefined,
    emergency_contact_encrypted: undefined
  };

  // Get addresses
  const addressQuery = `
    SELECT id, address_type, street_address_encrypted, city_encrypted,
           state_province, postal_code, country_code, is_primary
    FROM employee_addresses
    WHERE employee_id = $1
  `;
  const addressResult = await pool.query(addressQuery, [id]);
  decryptedEmployee.addresses = addressResult.rows.map(addr => ({
    ...addr,
    streetAddress: addr.street_address_encrypted ? decrypt(addr.street_address_encrypted) : null,
    city: addr.city_encrypted ? decrypt(addr.city_encrypted) : null,
    street_address_encrypted: undefined,
    city_encrypted: undefined
  }));

  return decryptedEmployee;
}

// Update employee
export async function updateEmployee(id, updateData, updatedBy) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Prepare encrypted fields
    const encryptedFields = {};
    if (updateData.firstName) encryptedFields.first_name_encrypted = encrypt(updateData.firstName);
    if (updateData.lastName) encryptedFields.last_name_encrypted = encrypt(updateData.lastName);
    if (updateData.phone) encryptedFields.phone_encrypted = encrypt(updateData.phone);
    if (updateData.dateOfBirth) encryptedFields.date_of_birth_encrypted = encrypt(updateData.dateOfBirth);
    if (updateData.nationalId) encryptedFields.national_id_encrypted = encrypt(updateData.nationalId);
    if (updateData.salary) encryptedFields.salary_encrypted = encrypt(updateData.salary.toString());
    if (updateData.emergencyContact) encryptedFields.emergency_contact_encrypted = encrypt(JSON.stringify(updateData.emergencyContact));

    // Build update query dynamically
    const updateFields = [];
    const values = [];
    let paramCount = 0;

    // Add regular fields
    const regularFields = ['email', 'department_id', 'manager_id', 'job_title', 'employment_type', 'work_location', 'currency', 'bio', 'status'];
    regularFields.forEach(field => {
      if (updateData[field] !== undefined) {
        paramCount++;
        updateFields.push(`${field} = $${paramCount}`);
        values.push(updateData[field]);
      }
    });

    // Add encrypted fields
    Object.entries(encryptedFields).forEach(([field, value]) => {
      paramCount++;
      updateFields.push(`${field} = $${paramCount}`);
      values.push(value);
    });

    // Add skills and certifications as JSONB
    if (updateData.skills) {
      paramCount++;
      updateFields.push(`skills = $${paramCount}`);
      values.push(JSON.stringify(updateData.skills));
    }

    if (updateData.certifications) {
      paramCount++;
      updateFields.push(`certifications = $${paramCount}`);
      values.push(JSON.stringify(updateData.certifications));
    }

    // Add updated_by and updated_at
    paramCount++;
    updateFields.push(`updated_by = $${paramCount}`);
    values.push(updatedBy);

    paramCount++;
    updateFields.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add employee ID as the last parameter
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE employees
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, employee_id, email, hire_date, status, department_id, manager_id, job_title, employment_type, work_location, currency, bio, skills, certifications, updated_at
    `;

    const result = await client.query(updateQuery, values);

    if (result.rows.length === 0) {
      throw new Error('Employee not found');
    }

    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Delete employee (soft delete)
export async function deleteEmployee(id, deletedBy) {
  const result = await pool.query(`
    UPDATE employees
    SET status = 'terminated',
        termination_date = CURRENT_DATE,
        updated_by = $2,
        updated_at = NOW()
    WHERE id = $1
    RETURNING id, employee_id, status, termination_date
  `, [id, deletedBy]);

  if (result.rows.length === 0) {
    throw new Error('Employee not found');
  }

  return result.rows[0];
}

// Get employee statistics
export async function getEmployeeStatistics() {
  const query = `
    SELECT
      COUNT(*) as total_employees,
      COUNT(CASE WHEN status = 'active' THEN 1 END) as active_employees,
      COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_employees,
      COUNT(CASE WHEN status = 'terminated' THEN 1 END) as terminated_employees,
      COUNT(CASE WHEN status = 'on_leave' THEN 1 END) as on_leave_employees,
      COUNT(CASE WHEN employment_type = 'full_time' THEN 1 END) as full_time_employees,
      COUNT(CASE WHEN employment_type = 'part_time' THEN 1 END) as part_time_employees,
      COUNT(CASE WHEN employment_type = 'contract' THEN 1 END) as contract_employees,
      COUNT(CASE WHEN work_location = 'remote' THEN 1 END) as remote_employees,
      COUNT(CASE WHEN work_location = 'office' THEN 1 END) as office_employees,
      COUNT(CASE WHEN work_location = 'hybrid' THEN 1 END) as hybrid_employees
    FROM employees
  `;

  const result = await pool.query(query);
  return result.rows[0];
}
