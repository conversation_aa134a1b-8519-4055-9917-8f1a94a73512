import pkg from 'pg';
const { Pool } = pkg;
import dotenv from 'dotenv';
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Initialize database schema
export async function initializeDatabase() {
  const client = await pool.connect();
  try {
    // Create enums
    await client.query(`
      DO $$ BEGIN
        CREATE TYPE employee_status AS ENUM ('active', 'inactive', 'terminated', 'on_leave');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
        CREATE TYPE employment_type_enum AS ENUM ('full_time', 'part_time', 'contract', 'intern', 'consultant');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
        CREATE TYPE work_location_enum AS ENUM ('office', 'remote', 'hybrid');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
        CREATE TYPE address_type_enum AS ENUM ('home', 'work', 'mailing', 'emergency');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
        CREATE TYPE document_type_enum AS ENUM ('resume', 'contract', 'id_document', 'certificate', 'performance_review', 'other');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
        CREATE TYPE onboarding_status_enum AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create users table if not exists
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'employee',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Create departments table
    await client.query(`
      CREATE TABLE IF NOT EXISTS departments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        description TEXT,
        head_of_department UUID REFERENCES users(id),
        cost_center VARCHAR(20),
        budget_allocated DECIMAL(15,2),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Create enhanced employees table
    await client.query(`
      CREATE TABLE IF NOT EXISTS employees (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        employee_id VARCHAR(20) UNIQUE NOT NULL,
        user_id UUID REFERENCES users(id),
        email VARCHAR(255) UNIQUE NOT NULL,
        first_name_encrypted BYTEA NOT NULL,
        last_name_encrypted BYTEA NOT NULL,
        phone_encrypted BYTEA,
        date_of_birth_encrypted BYTEA,
        national_id_encrypted BYTEA,
        hire_date DATE NOT NULL,
        termination_date DATE,
        status employee_status DEFAULT 'active',
        department_id UUID REFERENCES departments(id),
        manager_id UUID REFERENCES employees(id),
        job_title VARCHAR(100),
        employment_type employment_type_enum,
        work_location work_location_enum,
        salary_encrypted BYTEA,
        currency CHAR(3) DEFAULT 'USD',
        profile_picture_url VARCHAR(500),
        bio TEXT,
        skills JSONB,
        certifications JSONB,
        emergency_contact_encrypted BYTEA,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        created_by UUID REFERENCES users(id),
        updated_by UUID REFERENCES users(id)
      );
    `);

    console.log('Database schema initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  } finally {
    client.release();
  }
}

export default pool;
