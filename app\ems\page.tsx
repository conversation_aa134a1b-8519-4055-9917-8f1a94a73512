"use client";
import React, { useState } from 'react';
import { FaUserTie, FaPlus, FaTrash, FaEdit, FaSave, FaTimes, FaSearch, FaEye, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const initialEmployees = [
  {
    id: 1,
    fullName: '<PERSON>',
    dob: '1990-01-01',
    gender: 'Female',
    nationality: 'Nigerian',
    maritalStatus: 'Single',
    address: '12 Allen Avenue, Ikeja, Lagos',
    phone: '***********',
    email: '<EMAIL>',
    nin: '**********1',
    bvn: '**********',
    tin: '**********',
    nextOfKin: { name: '<PERSON>', relationship: 'Father', phone: '***********' },
    startDate: '2022-03-01',
    position: 'Software Engineer',
    department: 'IT',
    salary: '₦350,000',
    pensionPin: 'PEN1234567',
    bankName: 'GTBank',
    accountNumber: '**********',
    status: 'Active',
  },
];

const emptyEmployee = {
  fullName: '', dob: '', gender: '', nationality: '', maritalStatus: '', address: '', phone: '', email: '', nin: '', bvn: '', tin: '',
  nextOfKin: { name: '', relationship: '', phone: '' }, startDate: '', position: '', department: '', salary: '', pensionPin: '', bankName: '', accountNumber: '', status: 'Active',
};

const genders = ['Male', 'Female', 'Other'];
const maritalStatuses = ['Single', 'Married', 'Divorced', 'Widowed'];
const statuses = ['Active', 'Inactive'];
const departmentColors = {
  IT: '#2563eb',
  HR: '#0D9488',
  Finance: '#f59e42',
  Admin: '#a21caf',
  Legal: '#e11d48',
  Marketing: '#f59e42',
  Sales: '#059669',
  Other: '#64748b',
};

const formSteps = [
  'Personal Info',
  'Contact Info',
  'Employment Info',
  'Bank/Statutory Info',
  'Next of Kin',
];

export default function EMSPage() {
  const [employees, setEmployees] = useState(initialEmployees);
  const [modalOpen, setModalOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [form, setForm] = useState(emptyEmployee);
  const [search, setSearch] = useState('');
  const [viewEmployee, setViewEmployee] = useState(null);
  const [formStep, setFormStep] = useState(0);

  const openAddModal = () => {
    setForm(emptyEmployee);
    setEditMode(false);
    setFormStep(0);
    setModalOpen(true);
  };
  const openEditModal = (emp) => {
    setForm({ ...emp, nextOfKin: { ...emp.nextOfKin } });
    setEditMode(true);
    setSelectedEmployee(emp);
    setFormStep(0);
    setModalOpen(true);
  };
  const closeModal = () => {
    setModalOpen(false);
    setSelectedEmployee(null);
    setEditMode(false);
    setFormStep(0);
  };
  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('nextOfKin.')) {
      setForm({ ...form, nextOfKin: { ...form.nextOfKin, [name.split('.')[1]]: value } });
    } else {
      setForm({ ...form, [name]: value });
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editMode) {
      setEmployees(employees.map(emp => emp === selectedEmployee ? { ...form, id: emp.id } : emp));
    } else {
      setEmployees([
        ...employees,
        { ...form, id: employees.length ? Math.max(...employees.map(emp => emp.id)) + 1 : 1 },
      ]);
    }
    closeModal();
  };
  const handleDelete = (emp) => {
    if (window.confirm('Delete this employee?')) {
      setEmployees(employees.filter(e => e !== emp));
    }
  };
  const handleView = (emp) => setViewEmployee(emp);
  const closeView = () => setViewEmployee(null);

  const filteredEmployees = employees.filter(emp =>
    emp.fullName.toLowerCase().includes(search.toLowerCase()) ||
    emp.position.toLowerCase().includes(search.toLowerCase()) ||
    emp.department.toLowerCase().includes(search.toLowerCase()) ||
    emp.email.toLowerCase().includes(search.toLowerCase())
  );

  // Multi-step form fields
  const stepFields = [
    // Step 0: Personal Info
    <>
      <Input label="Full Name" name="fullName" value={form.fullName} onChange={handleChange} required />
      <Input label="Date of Birth" name="dob" value={form.dob} onChange={handleChange} type="date" required />
      <Select label="Gender" name="gender" value={form.gender} onChange={handleChange} options={genders} required />
      <Input label="Nationality" name="nationality" value={form.nationality} onChange={handleChange} required />
      <Select label="Marital Status" name="maritalStatus" value={form.maritalStatus} onChange={handleChange} options={maritalStatuses} required />
    </>,
    // Step 1: Contact Info
    <>
      <Input label="Address" name="address" value={form.address} onChange={handleChange} required />
      <Input label="Phone Number" name="phone" value={form.phone} onChange={handleChange} required />
      <Input label="Email" name="email" value={form.email} onChange={handleChange} type="email" required />
    </>,
    // Step 2: Employment Info
    <>
      <Input label="Position/Job Title" name="position" value={form.position} onChange={handleChange} required />
      <Input label="Department" name="department" value={form.department} onChange={handleChange} required />
      <Input label="Salary" name="salary" value={form.salary} onChange={handleChange} required />
      <Input label="Employment Start Date" name="startDate" value={form.startDate} onChange={handleChange} type="date" required />
      <Select label="Status" name="status" value={form.status} onChange={handleChange} options={statuses} required />
    </>,
    // Step 3: Bank/Statutory Info
    <>
      <Input label="NIN" name="nin" value={form.nin} onChange={handleChange} required />
      <Input label="BVN" name="bvn" value={form.bvn} onChange={handleChange} required />
      <Input label="TIN" name="tin" value={form.tin} onChange={handleChange} required />
      <Input label="Pension PIN" name="pensionPin" value={form.pensionPin} onChange={handleChange} required />
      <Input label="Bank Name" name="bankName" value={form.bankName} onChange={handleChange} required />
      <Input label="Account Number" name="accountNumber" value={form.accountNumber} onChange={handleChange} required />
    </>,
    // Step 4: Next of Kin
    <>
      <Input label="Next of Kin Name" name="nextOfKin.name" value={form.nextOfKin.name} onChange={handleChange} required />
      <Input label="Relationship" name="nextOfKin.relationship" value={form.nextOfKin.relationship} onChange={handleChange} required />
      <Input label="Phone" name="nextOfKin.phone" value={form.nextOfKin.phone} onChange={handleChange} required />
    </>,
  ];

  return (
    <div style={{ display: 'flex', minHeight: '100vh', fontFamily: 'Inter, Arial, sans-serif' }}>
      {/* Main Content */}
      <main style={{ flex: 1, padding: '2rem', maxWidth: 1200, margin: '0 auto' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 10, marginBottom: 24 }}>
          <FaUserTie style={{ fontSize: 28, color: '#0D9488' }} />
          <h2 style={{ margin: 0, fontSize: 24, fontWeight: 700 }}>Employee Management System (EMS)</h2>
          <button onClick={openAddModal} style={{ marginLeft: 'auto', background: '#0D9488', color: '#fff', border: 'none', borderRadius: 6, padding: '8px 18px', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 6, fontSize: 15 }}>
            <FaPlus /> Add Employee
          </button>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 18, gap: 10 }}>
          <div style={{ position: 'relative', flex: 1 }}>
            <FaSearch style={{ position: 'absolute', left: 10, top: 10, color: '#94a3b8' }} />
            <input
              type="text"
              placeholder="Search by name, position, department, or email..."
              value={search}
              onChange={e => setSearch(e.target.value)}
              style={{ width: '100%', padding: '8px 12px 8px 32px', borderRadius: 6, border: '1px solid #cbd5e1', fontSize: 15 }}
            />
          </div>
        </div>
        <div style={{ marginBottom: 32 }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(260px, 1fr))',
            gap: '1.5rem',
            padding: '0.5rem 0',
          }}>
            {filteredEmployees.length === 0 && (
              <div style={{ gridColumn: '1/-1', textAlign: 'center', color: '#64748b', padding: 24, background: '#fff', borderRadius: 14, boxShadow: '0 2px 16px #e0e7ef' }}>
                No employees found.
              </div>
            )}
            {filteredEmployees.map((emp) => (
              <div
                key={emp.id}
                style={{
                  background: '#fff',
                  borderRadius: 14,
                  boxShadow: '0 2px 16px #e0e7ef',
                  padding: '1.5rem 1.2rem',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 10,
                  transition: 'box-shadow 0.2s, transform 0.2s',
                  cursor: 'pointer',
                  position: 'relative',
                  minHeight: 140,
                }}
                onMouseOver={e => {
                  e.currentTarget.style.boxShadow = '0 4px 24px #bae6fd';
                  e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.boxShadow = '0 2px 16px #e0e7ef';
                  e.currentTarget.style.transform = 'none';
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                  <Avatar name={emp.fullName} />
                  <div>
                    <div style={{ fontWeight: 700, fontSize: 16, color: '#0D9488' }}>{emp.fullName}</div>
                    <div style={{ fontSize: 13, color: '#64748b', fontWeight: 500 }}>{emp.position}</div>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 10, marginTop: 4 }}>
                  <Badge color={departmentColors[emp.department] || '#64748b'}>{emp.department}</Badge>
                  <Badge color={emp.status === 'Active' ? '#059669' : '#e11d48'}>{emp.status}</Badge>
                </div>
                <div style={{ position: 'absolute', top: 12, right: 12, display: 'flex', gap: 6 }}>
                  <button onClick={() => handleView(emp)} style={iconBtn} title="View"><FaEye /></button>
                  <button onClick={() => openEditModal(emp)} style={iconBtn} title="Edit"><FaEdit /></button>
                  <button onClick={() => handleDelete(emp)} style={{ ...iconBtn, color: '#e11d48' }} title="Delete"><FaTrash /></button>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Modal for Add/Edit (Multi-step) */}
        {modalOpen && (
          <div style={modalOverlay}>
            <div style={modalBox}>
              <h3 style={{ margin: 0, marginBottom: 18 }}>{editMode ? 'Edit Employee' : 'Add Employee'}</h3>
              <div style={{ display: 'flex', alignItems: 'center', gap: 10, marginBottom: 18 }}>
                {formSteps.map((step, idx) => (
                  <div key={step} style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <div style={{
                      width: 22, height: 22, borderRadius: '50%', background: idx === formStep ? '#0D9488' : '#e5e7eb', color: idx === formStep ? '#fff' : '#64748b',
                      display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: 13, border: idx === formStep ? '2px solid #0D9488' : '2px solid #e5e7eb',
                    }}>{idx + 1}</div>
                    {idx < formSteps.length - 1 && <span style={{ width: 24, height: 2, background: '#e5e7eb', borderRadius: 1 }} />}
                  </div>
                ))}
              </div>
              <form onSubmit={handleSubmit} style={{ display: 'grid', gridTemplateColumns: '1fr', gap: 14, minWidth: 320, maxWidth: 420 }}>
                {stepFields[formStep]}
                <div style={{ display: 'flex', justifyContent: 'space-between', gap: 10, marginTop: 10 }}>
                  <button type="button" onClick={() => setFormStep(s => Math.max(0, s - 1))} style={{ ...modalBtn, background: '#e5e7eb', color: '#334155', visibility: formStep === 0 ? 'hidden' : 'visible' }}><FaChevronLeft /> Back</button>
                  {formStep < formSteps.length - 1 ? (
                    <button type="button" onClick={() => setFormStep(s => Math.min(formSteps.length - 1, s + 1))} style={{ ...modalBtn, background: '#0D9488', color: '#fff' }}>Next <FaChevronRight /></button>
                  ) : (
                    <button type="submit" style={{ ...modalBtn, background: '#0D9488', color: '#fff' }}><FaSave /> {editMode ? 'Save' : 'Add'}</button>
                  )}
                  <button type="button" onClick={closeModal} style={{ ...modalBtn, background: '#e5e7eb', color: '#334155' }}><FaTimes /> Cancel</button>
                </div>
              </form>
            </div>
          </div>
        )}
        {/* Modal for View */}
        {viewEmployee && (
          <div style={modalOverlay}>
            <div style={modalBox}>
              <h3 style={{ margin: 0, marginBottom: 18 }}>Employee Details</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12, fontSize: 15 }}>
                <div><b>Name:</b> {viewEmployee.fullName}</div>
                <div><b>DOB:</b> {viewEmployee.dob}</div>
                <div><b>Gender:</b> {viewEmployee.gender}</div>
                <div><b>Nationality:</b> {viewEmployee.nationality}</div>
                <div><b>Marital Status:</b> {viewEmployee.maritalStatus}</div>
                <div><b>Address:</b> {viewEmployee.address}</div>
                <div><b>Phone:</b> {viewEmployee.phone}</div>
                <div><b>Email:</b> {viewEmployee.email}</div>
                <div><b>NIN:</b> {viewEmployee.nin}</div>
                <div><b>BVN:</b> {viewEmployee.bvn}</div>
                <div><b>TIN:</b> {viewEmployee.tin}</div>
                <div><b>Pension PIN:</b> {viewEmployee.pensionPin}</div>
                <div><b>Bank Name:</b> {viewEmployee.bankName}</div>
                <div><b>Account Number:</b> {viewEmployee.accountNumber}</div>
                <div><b>Position:</b> {viewEmployee.position}</div>
                <div><b>Department:</b> {viewEmployee.department}</div>
                <div><b>Salary:</b> {viewEmployee.salary}</div>
                <div><b>Start Date:</b> {viewEmployee.startDate}</div>
                <div><b>Status:</b> <span style={{ color: viewEmployee.status === 'Active' ? '#059669' : '#e11d48', fontWeight: 600 }}>{viewEmployee.status}</span></div>
                <div style={{ gridColumn: '1/3' }}><b>Next of Kin:</b> {viewEmployee.nextOfKin.name} ({viewEmployee.nextOfKin.relationship}) - {viewEmployee.nextOfKin.phone}</div>
              </div>
              <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 18 }}>
                <button onClick={closeView} style={{ ...modalBtn, background: '#0D9488', color: '#fff' }}><FaTimes /> Close</button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

// UI helpers
const th = { padding: '10px 8px', borderBottom: '1px solid #e5e7eb', fontWeight: 700, textAlign: 'left', background: '#f1f5f9', position: 'sticky', top: 0, zIndex: 2 };
const td = { padding: '12px 8px', borderBottom: '1px solid #e5e7eb', background: 'none', verticalAlign: 'middle' };
const iconBtn = { background: 'none', border: 'none', color: '#2563eb', fontSize: 17, cursor: 'pointer', marginRight: 6, padding: 4, borderRadius: 4, transition: 'background 0.2s' };
const modalOverlay = { position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', background: 'rgba(30,41,59,0.18)', zIndex: 1000, display: 'flex', alignItems: 'center', justifyContent: 'center' };
const modalBox = { background: '#fff', borderRadius: 12, padding: 32, minWidth: 420, maxWidth: 600, boxShadow: '0 4px 32px #cbd5e1', position: 'relative' };
const modalBtn = { border: 'none', borderRadius: 6, padding: '8px 18px', fontWeight: 600, fontSize: 15, display: 'flex', alignItems: 'center', gap: 6, cursor: 'pointer' };

function Input({ label, ...props }) {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <label style={{ fontWeight: 500, marginBottom: 2 }}>{label}</label>
      <input {...props} style={{ ...props.style, padding: 7, borderRadius: 5, border: '1px solid #cbd5e1', fontSize: 15 }} />
    </div>
  );
}
function Select({ label, options, ...props }) {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <label style={{ fontWeight: 500, marginBottom: 2 }}>{label}</label>
      <select {...props} style={{ ...props.style, padding: 7, borderRadius: 5, border: '1px solid #cbd5e1', fontSize: 15 }}>
        <option value="">Select</option>
        {options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
      </select>
    </div>
  );
}
function Badge({ color, children }) {
  return <span style={{ background: color, color: '#fff', borderRadius: 8, padding: '2px 10px', fontWeight: 600, fontSize: 13 }}>{children}</span>;
}
function Avatar({ name }) {
  const initials = name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  const bg = '#0D9488';
  return (
    <span style={{ width: 34, height: 34, borderRadius: '50%', background: bg, color: '#fff', display: 'inline-flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: 16, boxShadow: '0 1px 4px #cbd5e1' }}>
      {initials}
    </span>
  );
}

// EmployeeRoles component for role management
function EmployeeRoles({ userId }) {
  const [roles, setRoles] = React.useState([]);
  const [userRoles, setUserRoles] = React.useState([]);
  const [assigning, setAssigning] = React.useState(false);
  const [selectedRole, setSelectedRole] = React.useState('');
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  React.useEffect(() => {
    let cancelled = false;
    setLoading(true);
    setError(null);
    Promise.all([
      fetch('http://localhost:4001/roles').then(r => r.ok ? r.json() : []),
      fetch(`http://localhost:4001/user-roles/${userId}`).then(r => r.ok ? r.json() : [])
    ]).then(([rolesData, userRolesData]) => {
      if (!cancelled) {
        setRoles(Array.isArray(rolesData) ? rolesData : []);
        setUserRoles(Array.isArray(userRolesData) ? userRolesData : []);
        setLoading(false);
      }
    }).catch(e => {
      if (!cancelled) {
        setError('Failed to load roles');
        setLoading(false);
      }
    });
    return () => { cancelled = true; };
  }, [userId, assigning]);

  const handleAssign = async () => {
    if (!selectedRole) return;
    setAssigning(true);
    try {
      await fetch('http://localhost:4001/assign-role', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userId, role_id: selectedRole, duration_hours: 72, context: {} })
      });
    } catch (e) {
      setError('Failed to assign role');
    }
    setAssigning(false);
    setSelectedRole('');
  };

  if (loading) return <span>Loading...</span>;
  if (error) return <span style={{ color: 'red', fontSize: 13 }}>{error}</span>;
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
        {(!userRoles || userRoles.length === 0) && <span style={{ color: '#64748b', fontSize: 13 }}>No roles</span>}
        {userRoles && userRoles.map(ur => {
          const role = roles.find(r => r.id === ur.role_id);
          return role ? <Badge key={ur.role_id} color="#2563eb">{role.name}</Badge> : null;
        })}
      </div>
      <div style={{ marginTop: 2 }}>
        <select value={selectedRole} onChange={e => setSelectedRole(e.target.value)} style={{ fontSize: 13, padding: 3, borderRadius: 4, border: '1px solid #cbd5e1', marginRight: 4 }}>
          <option value="">Assign role...</option>
          {roles.filter(r => !userRoles.some(ur => ur.role_id === r.id)).map(r => (
            <option key={r.id} value={r.id}>{r.name}</option>
          ))}
        </select>
        <button onClick={handleAssign} disabled={!selectedRole || assigning} style={{ fontSize: 13, padding: '3px 10px', borderRadius: 4, background: '#0D9488', color: '#fff', border: 'none', fontWeight: 600, cursor: 'pointer' }}>Assign</button>
      </div>
    </div>
  );
}
