import express from 'express';
import ndprConsent from '../middleware/ndprConsent.js';
import {
  addEmployee,
  listEmployees,
  getEmployee,
  updateEmployeeData,
  removeEmployee,
  getStatistics
} from '../controllers/employeeController.js';
import { authenticateToken } from '../middleware/auth.js';
import { validatePermissions } from '../middleware/permissions.js';

const router = express.Router();

// Public routes (with basic authentication)
router.get('/statistics', authenticateToken, getStatistics);

// Employee CRUD routes
router.post('/',
  authenticateToken,
  validatePermissions(['employee:create']),
  ndprConsent,
  addEmployee
);

router.get('/',
  authenticateToken,
  validatePermissions(['employee:read']),
  listEmployees
);

router.get('/:id',
  authenticateToken,
  validatePermissions(['employee:read']),
  getEmployee
);

router.put('/:id',
  authenticateToken,
  validatePermissions(['employee:update']),
  ndprConsent,
  updateEmployeeData
);

router.delete('/:id',
  authenticateToken,
  validatePermissions(['employee:delete']),
  removeEmployee
);

export default router;
