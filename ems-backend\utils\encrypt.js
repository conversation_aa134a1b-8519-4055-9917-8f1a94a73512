import crypto from 'crypto';

// Ensure we have a proper 32-byte key for AES-256
const getKey = () => {
  const keyString = process.env.AES_KEY || 'default-key-for-development-only-32';
  if (keyString.length < 32) {
    return Buffer.from(keyString.padEnd(32, '0'), 'utf8');
  }
  return Buffer.from(keyString.slice(0, 32), 'utf8');
};

const key = getKey();

export function encrypt(data) {
  if (!data) return null;

  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);

    // Convert data to string if it's not already
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);

    let encrypted = cipher.update(dataString, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const tag = cipher.getAuthTag();

    // Combine IV, tag, and encrypted data
    return Buffer.concat([iv, tag, Buffer.from(encrypted, 'hex')]);
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

export function decrypt(encryptedBuffer) {
  if (!encryptedBuffer) return null;

  try {
    // Extract IV (first 16 bytes), tag (next 16 bytes), and encrypted data (rest)
    const iv = encryptedBuffer.slice(0, 16);
    const tag = encryptedBuffer.slice(16, 32);
    const encrypted = encryptedBuffer.slice(32);

    const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
    decipher.setAuthTag(tag);

    let decrypted = decipher.update(encrypted, null, 'utf8');
    decrypted += decipher.final('utf8');

    // Try to parse as JSON, if it fails return as string
    try {
      return JSON.parse(decrypted);
    } catch {
      return decrypted;
    }
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

// Utility function to hash passwords
export function hashPassword(password) {
  const salt = crypto.randomBytes(16);
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512');
  return salt.toString('hex') + ':' + hash.toString('hex');
}

// Utility function to verify passwords
export function verifyPassword(password, hashedPassword) {
  const [salt, hash] = hashedPassword.split(':');
  const hashToVerify = crypto.pbkdf2Sync(password, Buffer.from(salt, 'hex'), 10000, 64, 'sha512');
  return hash === hashToVerify.toString('hex');
}

export function decrypt(buffer) {
  const iv = buffer.slice(0, 16);
  const tag = buffer.slice(16, 32);
  const encrypted = buffer.slice(32);
  const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
  decipher.setAuthTag(tag);
  let decrypted = decipher.update(encrypted, undefined, 'utf8');
  decrypted += decipher.final('utf8');
  return JSON.parse(decrypted);
}
