import {
  createEmployee,
  getAllEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
  getEmployeeStatistics
} from '../models/employee.js';
import { validateEmployeeData, validateUpdateData } from '../utils/validation.js';

// Create a new employee
export async function addEmployee(req, res) {
  try {
    // Validate input data
    const validationResult = validateEmployeeData(req.body);
    if (!validationResult.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.errors
      });
    }

    const employeeData = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email,
      phone: req.body.phone,
      dateOfBirth: req.body.dateOfBirth,
      nationalId: req.body.nationalId,
      hireDate: req.body.hireDate,
      departmentId: req.body.departmentId,
      managerId: req.body.managerId,
      jobTitle: req.body.jobTitle,
      employmentType: req.body.employmentType,
      workLocation: req.body.workLocation,
      salary: req.body.salary,
      currency: req.body.currency,
      bio: req.body.bio,
      skills: req.body.skills,
      certifications: req.body.certifications,
      emergencyContact: req.body.emergencyContact,
      address: req.body.address,
      createdBy: req.user?.id || req.body.createdBy
    };

    const employee = await createEmployee(employeeData);

    res.status(201).json({
      success: true,
      message: 'Employee created successfully',
      data: employee
    });
  } catch (error) {
    console.error('Error creating employee:', error);
    res.status(500).json({
      error: 'Failed to create employee',
      message: error.message
    });
  }
}

// Get all employees with pagination and filtering
export async function listEmployees(req, res) {
  try {
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 50,
      search: req.query.search || '',
      department: req.query.department || '',
      status: req.query.status || '',
      sortBy: req.query.sortBy || 'created_at',
      sortOrder: req.query.sortOrder || 'DESC'
    };

    // Validate sort parameters
    const validSortFields = ['created_at', 'hire_date', 'employee_id', 'email', 'job_title'];
    if (!validSortFields.includes(options.sortBy)) {
      options.sortBy = 'created_at';
    }

    if (!['ASC', 'DESC'].includes(options.sortOrder.toUpperCase())) {
      options.sortOrder = 'DESC';
    }

    const result = await getAllEmployees(options);

    res.json({
      success: true,
      data: result.employees,
      pagination: {
        page: result.page,
        limit: result.limit,
        totalCount: result.totalCount,
        totalPages: result.totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching employees:', error);
    res.status(500).json({
      error: 'Failed to fetch employees',
      message: error.message
    });
  }
}

// Get employee by ID
export async function getEmployee(req, res) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Employee ID is required' });
    }

    const employee = await getEmployeeById(id);

    if (!employee) {
      return res.status(404).json({ error: 'Employee not found' });
    }

    res.json({
      success: true,
      data: employee
    });
  } catch (error) {
    console.error('Error fetching employee:', error);
    res.status(500).json({
      error: 'Failed to fetch employee',
      message: error.message
    });
  }
}

// Update employee
export async function updateEmployeeData(req, res) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Employee ID is required' });
    }

    // Validate update data
    const validationResult = validateUpdateData(req.body);
    if (!validationResult.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.errors
      });
    }

    const updatedEmployee = await updateEmployee(
      id,
      req.body,
      req.user?.id || req.body.updatedBy
    );

    res.json({
      success: true,
      message: 'Employee updated successfully',
      data: updatedEmployee
    });
  } catch (error) {
    console.error('Error updating employee:', error);
    if (error.message === 'Employee not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({
      error: 'Failed to update employee',
      message: error.message
    });
  }
}

// Delete employee (soft delete)
export async function removeEmployee(req, res) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Employee ID is required' });
    }

    const deletedEmployee = await deleteEmployee(
      id,
      req.user?.id || req.body.deletedBy
    );

    res.json({
      success: true,
      message: 'Employee terminated successfully',
      data: deletedEmployee
    });
  } catch (error) {
    console.error('Error deleting employee:', error);
    if (error.message === 'Employee not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({
      error: 'Failed to terminate employee',
      message: error.message
    });
  }
}

// Get employee statistics
export async function getStatistics(req, res) {
  try {
    const stats = await getEmployeeStatistics();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
}
