// Validation utilities for employee data

// Email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone validation regex (international format)
const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

// Employee ID validation regex
const employeeIdRegex = /^EMP\d{4}$/;

// Validate employee creation data
export function validateEmployeeData(data) {
  const errors = [];

  // Required fields validation
  if (!data.firstName || typeof data.firstName !== 'string' || data.firstName.trim().length < 2) {
    errors.push('First name is required and must be at least 2 characters long');
  }

  if (!data.lastName || typeof data.lastName !== 'string' || data.lastName.trim().length < 2) {
    errors.push('Last name is required and must be at least 2 characters long');
  }

  if (!data.email || !emailRegex.test(data.email)) {
    errors.push('Valid email address is required');
  }

  if (!data.hireDate || !isValidDate(data.hireDate)) {
    errors.push('Valid hire date is required');
  }

  if (!data.jobTitle || typeof data.jobTitle !== 'string' || data.jobTitle.trim().length < 2) {
    errors.push('Job title is required and must be at least 2 characters long');
  }

  // Optional field validations
  if (data.phone && !phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
    errors.push('Invalid phone number format');
  }

  if (data.dateOfBirth && !isValidDate(data.dateOfBirth)) {
    errors.push('Invalid date of birth format');
  }

  if (data.dateOfBirth && new Date(data.dateOfBirth) > new Date()) {
    errors.push('Date of birth cannot be in the future');
  }

  if (data.hireDate && new Date(data.hireDate) > new Date()) {
    errors.push('Hire date cannot be in the future');
  }

  // Employment type validation
  const validEmploymentTypes = ['full_time', 'part_time', 'contract', 'intern', 'consultant'];
  if (data.employmentType && !validEmploymentTypes.includes(data.employmentType)) {
    errors.push('Invalid employment type');
  }

  // Work location validation
  const validWorkLocations = ['office', 'remote', 'hybrid'];
  if (data.workLocation && !validWorkLocations.includes(data.workLocation)) {
    errors.push('Invalid work location');
  }

  // Status validation
  const validStatuses = ['active', 'inactive', 'terminated', 'on_leave'];
  if (data.status && !validStatuses.includes(data.status)) {
    errors.push('Invalid employee status');
  }

  // Salary validation
  if (data.salary && (isNaN(data.salary) || parseFloat(data.salary) < 0)) {
    errors.push('Salary must be a positive number');
  }

  // Currency validation
  const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'NGN'];
  if (data.currency && !validCurrencies.includes(data.currency)) {
    errors.push('Invalid currency code');
  }

  // Skills validation
  if (data.skills && !Array.isArray(data.skills)) {
    errors.push('Skills must be an array');
  }

  // Certifications validation
  if (data.certifications && !Array.isArray(data.certifications)) {
    errors.push('Certifications must be an array');
  }

  // Emergency contact validation
  if (data.emergencyContact) {
    if (!data.emergencyContact.name || data.emergencyContact.name.trim().length < 2) {
      errors.push('Emergency contact name is required');
    }
    if (!data.emergencyContact.phone || !phoneRegex.test(data.emergencyContact.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Emergency contact phone number is required and must be valid');
    }
    if (!data.emergencyContact.relationship || data.emergencyContact.relationship.trim().length < 2) {
      errors.push('Emergency contact relationship is required');
    }
  }

  // Address validation
  if (data.address) {
    if (!data.address.streetAddress || data.address.streetAddress.trim().length < 5) {
      errors.push('Street address is required and must be at least 5 characters long');
    }
    if (!data.address.city || data.address.city.trim().length < 2) {
      errors.push('City is required');
    }
    if (!data.address.countryCode || data.address.countryCode.length !== 2) {
      errors.push('Valid country code is required (2 characters)');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate employee update data
export function validateUpdateData(data) {
  const errors = [];

  // Only validate fields that are being updated
  if (data.firstName !== undefined) {
    if (!data.firstName || typeof data.firstName !== 'string' || data.firstName.trim().length < 2) {
      errors.push('First name must be at least 2 characters long');
    }
  }

  if (data.lastName !== undefined) {
    if (!data.lastName || typeof data.lastName !== 'string' || data.lastName.trim().length < 2) {
      errors.push('Last name must be at least 2 characters long');
    }
  }

  if (data.email !== undefined) {
    if (!data.email || !emailRegex.test(data.email)) {
      errors.push('Valid email address is required');
    }
  }

  if (data.phone !== undefined && data.phone) {
    if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Invalid phone number format');
    }
  }

  if (data.dateOfBirth !== undefined && data.dateOfBirth) {
    if (!isValidDate(data.dateOfBirth)) {
      errors.push('Invalid date of birth format');
    }
    if (new Date(data.dateOfBirth) > new Date()) {
      errors.push('Date of birth cannot be in the future');
    }
  }

  if (data.jobTitle !== undefined) {
    if (!data.jobTitle || typeof data.jobTitle !== 'string' || data.jobTitle.trim().length < 2) {
      errors.push('Job title must be at least 2 characters long');
    }
  }

  // Employment type validation
  const validEmploymentTypes = ['full_time', 'part_time', 'contract', 'intern', 'consultant'];
  if (data.employmentType !== undefined && !validEmploymentTypes.includes(data.employmentType)) {
    errors.push('Invalid employment type');
  }

  // Work location validation
  const validWorkLocations = ['office', 'remote', 'hybrid'];
  if (data.workLocation !== undefined && !validWorkLocations.includes(data.workLocation)) {
    errors.push('Invalid work location');
  }

  // Status validation
  const validStatuses = ['active', 'inactive', 'terminated', 'on_leave'];
  if (data.status !== undefined && !validStatuses.includes(data.status)) {
    errors.push('Invalid employee status');
  }

  // Salary validation
  if (data.salary !== undefined && data.salary) {
    if (isNaN(data.salary) || parseFloat(data.salary) < 0) {
      errors.push('Salary must be a positive number');
    }
  }

  // Currency validation
  const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'NGN'];
  if (data.currency !== undefined && !validCurrencies.includes(data.currency)) {
    errors.push('Invalid currency code');
  }

  // Skills validation
  if (data.skills !== undefined && !Array.isArray(data.skills)) {
    errors.push('Skills must be an array');
  }

  // Certifications validation
  if (data.certifications !== undefined && !Array.isArray(data.certifications)) {
    errors.push('Certifications must be an array');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Helper function to validate date format
function isValidDate(dateString) {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date) && dateString.match(/^\d{4}-\d{2}-\d{2}$/);
}

// Validate employee ID format
export function validateEmployeeId(employeeId) {
  return employeeIdRegex.test(employeeId);
}

// Validate UUID format
export function validateUUID(uuid) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Sanitize input data
export function sanitizeInput(data) {
  if (typeof data === 'string') {
    return data.trim().replace(/[<>]/g, '');
  }
  if (typeof data === 'object' && data !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  return data;
}
