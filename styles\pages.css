/* Employee Management System (EMS) Page Styles */

.ems-root {
  display: flex;
  min-height: 100vh;
  font-family: Inter, Arial, sans-serif;
}

.ems-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.ems-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 24px;
}

.ems-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.ems-add-btn {
  margin-left: auto;
  background: #0D9488;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 15px;
  cursor: pointer;
}

.ems-search-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  gap: 10px;
}

.ems-search-box {
  position: relative;
  flex: 1;
}

.ems-search-input {
  width: 100%;
  padding: 8px 12px 8px 32px;
  border-radius: 6px;
  border: 1px solid #cbd5e1;
  font-size: 15px;
}

.ems-search-icon {
  position: absolute;
  left: 10px;
  top: 10px;
  color: #94a3b8;
}

.ems-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem 0;
  margin-bottom: 32px;
}

.ems-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 16px #e0e7ef;
  padding: 1.5rem 1.2rem;
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: box-shadow 0.2s, transform 0.2s;
  cursor: pointer;
  position: relative;
  min-height: 140px;
}
.ems-card:hover {
  box-shadow: 0 4px 24px #bae6fd;
  transform: translateY(-2px) scale(1.02);
}

.ems-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ems-card-name {
  font-weight: 700;
  font-size: 16px;
  color: #0D9488;
}

.ems-card-position {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.ems-card-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 4px;
}

.ems-card-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 6px;
}

.ems-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30,41,59,0.18);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ems-modal-box {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  min-width: 420px;
  max-width: 600px;
  box-shadow: 0 4px 32px #cbd5e1;
  position: relative;
}

.ems-modal-title {
  margin: 0;
  margin-bottom: 18px;
}

.ems-modal-stepper {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 18px;
}

.ems-modal-step {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ems-modal-step-circle {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 13px;
  border: 2px solid #e5e7eb;
}
.ems-modal-step-circle.active {
  background: #0D9488;
  color: #fff;
  border: 2px solid #0D9488;
}

.ems-modal-step-bar {
  width: 24px;
  height: 2px;
  background: #e5e7eb;
  border-radius: 1px;
}

.ems-modal-form {
  display: grid;
  grid-template-columns: 1fr;
  gap: 14px;
  min-width: 320px;
  max-width: 420px;
}

.ems-modal-form-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 10px;
}

.ems-modal-btn {
  border: none;
  border-radius: 6px;
  padding: 8px 18px;
  font-weight: 600;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.ems-modal-btn.primary {
  background: #0D9488;
  color: #fff;
}

.ems-modal-btn.secondary {
  background: #e5e7eb;
  color: #334155;
}

.ems-modal-btn.danger {
  background: #e11d48;
  color: #fff;
}

.ems-modal-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  font-size: 15px;
}

.ems-modal-details-row {
  grid-column: 1/3;
}

.ems-modal-details-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 18px;
}
