// Role-based permissions system for EMS

// Define permissions for each role
const ROLE_PERMISSIONS = {
  admin: [
    'employee:create',
    'employee:read',
    'employee:update',
    'employee:delete',
    'department:create',
    'department:read',
    'department:update',
    'department:delete',
    'user:create',
    'user:read',
    'user:update',
    'user:delete',
    'system:configure',
    'audit:read',
    'reports:generate',
    'payroll:read',
    'payroll:process',
    'performance:read',
    'performance:write'
  ],
  hr: [
    'employee:create',
    'employee:read',
    'employee:update',
    'department:read',
    'department:update',
    'user:create',
    'user:read',
    'user:update',
    'audit:read',
    'reports:generate',
    'performance:read',
    'performance:write',
    'onboarding:manage',
    'offboarding:manage'
  ],
  manager: [
    'employee:read',
    'employee:update', // Limited to direct reports
    'department:read',
    'reports:generate', // Limited scope
    'performance:read', // Limited to direct reports
    'performance:write', // Limited to direct reports
    'team:manage'
  ],
  finance: [
    'employee:read', // Limited fields
    'payroll:read',
    'payroll:process',
    'reports:generate', // Financial reports only
    'audit:read' // Financial audit only
  ],
  employee: [
    'employee:read', // Own data only
    'employee:update', // Own data only, limited fields
    'performance:read', // Own performance only
    'time:track',
    'leave:request'
  ]
};

// Special permissions that require additional context checks
const CONTEXT_SENSITIVE_PERMISSIONS = [
  'employee:update',
  'employee:read',
  'performance:read',
  'performance:write',
  'reports:generate'
];

// Middleware to validate permissions
export function validatePermissions(requiredPermissions) {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please authenticate to access this resource'
        });
      }

      const userRole = req.user.role;
      const userPermissions = ROLE_PERMISSIONS[userRole] || [];

      // Check if user has all required permissions
      const hasPermissions = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );

      if (!hasPermissions) {
        return res.status(403).json({
          error: 'Insufficient permissions',
          message: 'You do not have the required permissions to perform this action',
          required: requiredPermissions,
          userRole: userRole
        });
      }

      // For context-sensitive permissions, perform additional checks
      const contextSensitivePerms = requiredPermissions.filter(perm => 
        CONTEXT_SENSITIVE_PERMISSIONS.includes(perm)
      );

      if (contextSensitivePerms.length > 0) {
        const contextCheck = await validateContextSensitivePermissions(
          req, 
          contextSensitivePerms, 
          userRole
        );

        if (!contextCheck.allowed) {
          return res.status(403).json({
            error: 'Access denied',
            message: contextCheck.reason
          });
        }
      }

      next();
    } catch (error) {
      console.error('Error in permissions middleware:', error);
      return res.status(500).json({
        error: 'Permission validation error',
        message: 'An error occurred while validating permissions'
      });
    }
  };
}

// Validate context-sensitive permissions
async function validateContextSensitivePermissions(req, permissions, userRole) {
  const employeeId = req.params.id;
  
  // Admin always has access
  if (userRole === 'admin') {
    return { allowed: true };
  }

  // HR has broad access but with some limitations
  if (userRole === 'hr') {
    // HR can access most employee data but may have restrictions on salary info
    if (permissions.includes('employee:read') && req.query.includeSalary === 'true') {
      // Only allow salary access for HR with explicit permission
      return { allowed: true }; // For now, allow HR to see salary
    }
    return { allowed: true };
  }

  // Manager permissions - can only access direct reports
  if (userRole === 'manager') {
    if (employeeId && (permissions.includes('employee:read') || permissions.includes('employee:update'))) {
      const isDirectReport = await checkDirectReport(req.user.employee_id, employeeId);
      if (!isDirectReport) {
        return { 
          allowed: false, 
          reason: 'You can only access data for your direct reports' 
        };
      }
    }
    return { allowed: true };
  }

  // Finance permissions - limited employee data access
  if (userRole === 'finance') {
    if (permissions.includes('employee:read')) {
      // Finance can only access payroll-related employee data
      req.limitedFields = ['id', 'employee_id', 'email', 'job_title', 'department_id', 'salary', 'currency'];
    }
    return { allowed: true };
  }

  // Employee permissions - can only access own data
  if (userRole === 'employee') {
    if (employeeId && employeeId !== req.user.employee_id) {
      return { 
        allowed: false, 
        reason: 'You can only access your own employee data' 
      };
    }
    
    // Limit fields that employees can update
    if (permissions.includes('employee:update')) {
      const allowedUpdateFields = [
        'phone', 'address', 'emergencyContact', 'bio', 'skills', 'certifications'
      ];
      
      const updateFields = Object.keys(req.body);
      const unauthorizedFields = updateFields.filter(field => 
        !allowedUpdateFields.includes(field)
      );
      
      if (unauthorizedFields.length > 0) {
        return { 
          allowed: false, 
          reason: `You cannot update the following fields: ${unauthorizedFields.join(', ')}` 
        };
      }
    }
    
    return { allowed: true };
  }

  return { 
    allowed: false, 
    reason: 'Unknown role or insufficient permissions' 
  };
}

// Check if an employee is a direct report of a manager
async function checkDirectReport(managerId, employeeId) {
  try {
    const pool = (await import('../db.js')).default;
    const query = `
      SELECT 1 FROM employees 
      WHERE id = $1 AND manager_id = $2
    `;
    
    const result = await pool.query(query, [employeeId, managerId]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error checking direct report relationship:', error);
    return false;
  }
}

// Middleware to check specific permission
export function requirePermission(permission) {
  return validatePermissions([permission]);
}

// Middleware to check if user has any of the specified permissions
export function requireAnyPermission(permissions) {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please authenticate to access this resource'
        });
      }

      const userRole = req.user.role;
      const userPermissions = ROLE_PERMISSIONS[userRole] || [];

      // Check if user has at least one of the required permissions
      const hasAnyPermission = permissions.some(permission => 
        userPermissions.includes(permission)
      );

      if (!hasAnyPermission) {
        return res.status(403).json({
          error: 'Insufficient permissions',
          message: 'You do not have any of the required permissions to perform this action',
          required: permissions,
          userRole: userRole
        });
      }

      next();
    } catch (error) {
      console.error('Error in any permission middleware:', error);
      return res.status(500).json({
        error: 'Permission validation error',
        message: 'An error occurred while validating permissions'
      });
    }
  };
}

// Get permissions for a role
export function getRolePermissions(role) {
  return ROLE_PERMISSIONS[role] || [];
}

// Check if a role has a specific permission
export function roleHasPermission(role, permission) {
  const permissions = ROLE_PERMISSIONS[role] || [];
  return permissions.includes(permission);
}

// Middleware to add user permissions to request object
export function addUserPermissions(req, res, next) {
  if (req.user) {
    req.userPermissions = getRolePermissions(req.user.role);
  }
  next();
}
