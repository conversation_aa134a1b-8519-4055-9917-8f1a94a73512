{"name": "ems-backend", "version": "1.0.0", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "lint": "eslint ."}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.1", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "uuid": "^9.0.0", "multer": "^1.4.5-lts.1", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.42.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["hrms", "employee-management", "nodejs", "express", "postgresql"], "author": "PeopleNest Team", "license": "MIT", "description": "Employee Management System Backend API"}