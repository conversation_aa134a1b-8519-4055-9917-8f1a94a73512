'use client';
import { useState } from "react";
import RoleBasedSidebar from "../components/RoleBasedSidebar";
import SkillRadarChart from "../components/SkillRadarChart";
import AIActionChips from "../components/AIActionChips";
import { useSpring, animated } from "@react-spring/web";

const employeeSkills = [
  { skill: "React", level: 85 },
  { skill: "TypeScript", level: 80 },
  { skill: "UI/UX", level: 90 },
  { skill: "Node.js", level: 70 },
  { skill: "AI Integration", level: 60 },
];

export default function EmployeeProfilePage() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const mainSpring = useSpring({
    background: darkMode
      ? "rgba(30,41,59,0.85)"
      : "rgba(255,255,255,0.7)",
    color: darkMode ? "#f1f5f9" : "#475569",
    boxShadow:
      "0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(13, 148, 136, 0.10)",
    borderRadius: "24px",
    padding: "2rem",
    margin: "2rem auto",
    maxWidth: "900px",
    width: "100%",
    minHeight: "80vh",
  });

  return (
    <animated.main style={mainSpring} className="modern-main-content">
      <div style={{ display: "flex", justifyContent: "flex-end", alignItems: "center" }}>
        <button
          className="dark-mode-toggle"
          aria-label="Toggle dark mode"
          onClick={() => setDarkMode((d) => !d)}
        >
          {darkMode ? "🌞" : "🌙"}
        </button>
      </div>
      <section className="profile-header" style={{ marginTop: "2rem" }}>
        <div className="avatar-glass" aria-label="User avatar" tabIndex={0}>
          JD
        </div>
        <div>
          <h2 style={{ margin: 0 }}>Jane Doe</h2>
          <p style={{ margin: 0 }}>Lead Frontend Engineer</p>
          <p style={{ margin: "0.5rem 0" }}>
            <span style={{ fontWeight: 600 }}>Skills:</span> React, TypeScript, UI/UX, Node.js, AI Integration
          </p>
          <AIActionChips actions={["Promote", "Schedule Review", "View History"]} animate />
        </div>
      </section>
      <section className="skill-matrix-card neumorph-card" style={{ marginTop: "2rem" }}>
        <h3 style={{ marginTop: 0 }}>Skill Matrix</h3>
        <SkillRadarChart data={employeeSkills} darkMode={darkMode} />
      </section>
      <section className="performance-timeline-section">
        <h3>Performance Timeline</h3>
        <div className="timeline-placeholder" tabIndex={0} aria-label="Performance timeline placeholder">
          <svg width="100%" height="60" viewBox="0 0 400 60" fill="none">
            <rect x="10" y="25" width="380" height="10" rx="5" fill="#e0e7ef" />
            <circle cx="50" cy="30" r="12" fill="#2563EB" />
            <circle cx="150" cy="30" r="12" fill="#0D9488" />
            <circle cx="250" cy="30" r="12" fill="#2563EB" />
            <circle cx="350" cy="30" r="12" fill="#0D9488" />
          </svg>
          <div style={{ marginTop: 8, color: darkMode ? "#f1f5f9" : "#475569" }}>
            <span>2021</span>
            <span style={{ margin: "0 60px" }}>2022</span>
            <span style={{ margin: "0 60px" }}>2023</span>
            <span style={{ margin: "0 60px" }}>2024</span>
          </div>
        </div>
      </section>
    </animated.main>
  );
}
