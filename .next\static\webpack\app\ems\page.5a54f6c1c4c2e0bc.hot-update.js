"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ems/page",{

/***/ "(app-client)/./app/ems/page.tsx":
/*!**************************!*\
  !*** ./app/ems/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EMSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-icons/fa */ \"(app-client)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst initialEmployees = [\n    {\n        id: 1,\n        fullName: \"Alice Johnson\",\n        dob: \"1990-01-01\",\n        gender: \"Female\",\n        nationality: \"Nigerian\",\n        maritalStatus: \"Single\",\n        address: \"12 Allen Avenue, Ikeja, Lagos\",\n        phone: \"***********\",\n        email: \"<EMAIL>\",\n        nin: \"**********1\",\n        bvn: \"**********\",\n        tin: \"**********\",\n        nextOfKin: {\n            name: \"John Johnson\",\n            relationship: \"Father\",\n            phone: \"***********\"\n        },\n        startDate: \"2022-03-01\",\n        position: \"Software Engineer\",\n        department: \"IT\",\n        salary: \"₦350,000\",\n        pensionPin: \"PEN1234567\",\n        bankName: \"GTBank\",\n        accountNumber: \"**********\",\n        status: \"Active\"\n    }\n];\nconst emptyEmployee = {\n    fullName: \"\",\n    dob: \"\",\n    gender: \"\",\n    nationality: \"\",\n    maritalStatus: \"\",\n    address: \"\",\n    phone: \"\",\n    email: \"\",\n    nin: \"\",\n    bvn: \"\",\n    tin: \"\",\n    nextOfKin: {\n        name: \"\",\n        relationship: \"\",\n        phone: \"\"\n    },\n    startDate: \"\",\n    position: \"\",\n    department: \"\",\n    salary: \"\",\n    pensionPin: \"\",\n    bankName: \"\",\n    accountNumber: \"\",\n    status: \"Active\"\n};\nconst genders = [\n    \"Male\",\n    \"Female\",\n    \"Other\"\n];\nconst maritalStatuses = [\n    \"Single\",\n    \"Married\",\n    \"Divorced\",\n    \"Widowed\"\n];\nconst statuses = [\n    \"Active\",\n    \"Inactive\"\n];\nconst departmentColors = {\n    IT: \"#2563eb\",\n    HR: \"#0D9488\",\n    Finance: \"#f59e42\",\n    Admin: \"#a21caf\",\n    Legal: \"#e11d48\",\n    Marketing: \"#f59e42\",\n    Sales: \"#059669\",\n    Other: \"#64748b\"\n};\nconst formSteps = [\n    \"Personal Info\",\n    \"Contact Info\",\n    \"Employment Info\",\n    \"Bank/Statutory Info\",\n    \"Next of Kin\"\n];\nfunction EMSPage() {\n    _s();\n    const [employees, setEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialEmployees);\n    const [modalOpen, setModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editMode, setEditMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEmployee, setSelectedEmployee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [form, setForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(emptyEmployee);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [viewEmployee, setViewEmployee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formStep, setFormStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const openAddModal = ()=>{\n        setForm(emptyEmployee);\n        setEditMode(false);\n        setFormStep(0);\n        setModalOpen(true);\n    };\n    const openEditModal = (emp)=>{\n        setForm({\n            ...emp,\n            nextOfKin: {\n                ...emp.nextOfKin\n            }\n        });\n        setEditMode(true);\n        setSelectedEmployee(emp);\n        setFormStep(0);\n        setModalOpen(true);\n    };\n    const closeModal = ()=>{\n        setModalOpen(false);\n        setSelectedEmployee(null);\n        setEditMode(false);\n        setFormStep(0);\n    };\n    const handleChange = (e)=>{\n        const { name , value  } = e.target;\n        if (name.startsWith(\"nextOfKin.\")) {\n            setForm({\n                ...form,\n                nextOfKin: {\n                    ...form.nextOfKin,\n                    [name.split(\".\")[1]]: value\n                }\n            });\n        } else {\n            setForm({\n                ...form,\n                [name]: value\n            });\n        }\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (editMode) {\n            setEmployees(employees.map((emp)=>emp === selectedEmployee ? {\n                    ...form,\n                    id: emp.id\n                } : emp));\n        } else {\n            setEmployees([\n                ...employees,\n                {\n                    ...form,\n                    id: employees.length ? Math.max(...employees.map((emp)=>emp.id)) + 1 : 1\n                }\n            ]);\n        }\n        closeModal();\n    };\n    const handleDelete = (emp)=>{\n        if (window.confirm(\"Delete this employee?\")) {\n            setEmployees(employees.filter((e)=>e !== emp));\n        }\n    };\n    const handleView = (emp)=>setViewEmployee(emp);\n    const closeView = ()=>setViewEmployee(null);\n    const filteredEmployees = employees.filter((emp)=>emp.fullName.toLowerCase().includes(search.toLowerCase()) || emp.position.toLowerCase().includes(search.toLowerCase()) || emp.department.toLowerCase().includes(search.toLowerCase()) || emp.email.toLowerCase().includes(search.toLowerCase()));\n    // Multi-step form fields\n    const stepFields = [\n        // Step 0: Personal Info\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Full Name\",\n                    name: \"fullName\",\n                    value: form.fullName,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Date of Birth\",\n                    name: \"dob\",\n                    value: form.dob,\n                    onChange: handleChange,\n                    type: \"date\",\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                    label: \"Gender\",\n                    name: \"gender\",\n                    value: form.gender,\n                    onChange: handleChange,\n                    options: genders,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Nationality\",\n                    name: \"nationality\",\n                    value: form.nationality,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                    label: \"Marital Status\",\n                    name: \"maritalStatus\",\n                    value: form.maritalStatus,\n                    onChange: handleChange,\n                    options: maritalStatuses,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true),\n        // Step 1: Contact Info\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Address\",\n                    name: \"address\",\n                    value: form.address,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Phone Number\",\n                    name: \"phone\",\n                    value: form.phone,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Email\",\n                    name: \"email\",\n                    value: form.email,\n                    onChange: handleChange,\n                    type: \"email\",\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true),\n        // Step 2: Employment Info\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Position/Job Title\",\n                    name: \"position\",\n                    value: form.position,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Department\",\n                    name: \"department\",\n                    value: form.department,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Salary\",\n                    name: \"salary\",\n                    value: form.salary,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Employment Start Date\",\n                    name: \"startDate\",\n                    value: form.startDate,\n                    onChange: handleChange,\n                    type: \"date\",\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                    label: \"Status\",\n                    name: \"status\",\n                    value: form.status,\n                    onChange: handleChange,\n                    options: statuses,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true),\n        // Step 3: Bank/Statutory Info\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"NIN\",\n                    name: \"nin\",\n                    value: form.nin,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"BVN\",\n                    name: \"bvn\",\n                    value: form.bvn,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"TIN\",\n                    name: \"tin\",\n                    value: form.tin,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Pension PIN\",\n                    name: \"pensionPin\",\n                    value: form.pensionPin,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Bank Name\",\n                    name: \"bankName\",\n                    value: form.bankName,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Account Number\",\n                    name: \"accountNumber\",\n                    value: form.accountNumber,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true),\n        // Step 4: Next of Kin\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Next of Kin Name\",\n                    name: \"nextOfKin.name\",\n                    value: form.nextOfKin.name,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Relationship\",\n                    name: \"nextOfKin.relationship\",\n                    value: form.nextOfKin.relationship,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                    label: \"Phone\",\n                    name: \"nextOfKin.phone\",\n                    value: form.nextOfKin.phone,\n                    onChange: handleChange,\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true)\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            fontFamily: \"Inter, Arial, sans-serif\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            style: {\n                flex: 1,\n                padding: \"2rem\",\n                maxWidth: 1200,\n                margin: \"0 auto\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 10,\n                        marginBottom: 24\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaUserTie, {\n                            style: {\n                                fontSize: 28,\n                                color: \"#0D9488\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                margin: 0,\n                                fontSize: 24,\n                                fontWeight: 700\n                            },\n                            children: \"Employee Management System (EMS)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: openAddModal,\n                            style: {\n                                marginLeft: \"auto\",\n                                background: \"#0D9488\",\n                                color: \"#fff\",\n                                border: \"none\",\n                                borderRadius: 6,\n                                padding: \"8px 18px\",\n                                fontWeight: 600,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 6,\n                                fontSize: 15\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaPlus, {}, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                \" Add Employee\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        marginBottom: 18,\n                        gap: 10\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            flex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaSearch, {\n                                style: {\n                                    position: \"absolute\",\n                                    left: 10,\n                                    top: 10,\n                                    color: \"#94a3b8\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search by name, position, department, or email...\",\n                                value: search,\n                                onChange: (e)=>setSearch(e.target.value),\n                                style: {\n                                    width: \"100%\",\n                                    padding: \"8px 12px 8px 32px\",\n                                    borderRadius: 6,\n                                    border: \"1px solid #cbd5e1\",\n                                    fontSize: 15\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginBottom: 32\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(260px, 1fr))\",\n                            gap: \"1.5rem\",\n                            padding: \"0.5rem 0\"\n                        },\n                        children: [\n                            filteredEmployees.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    gridColumn: \"1/-1\",\n                                    textAlign: \"center\",\n                                    color: \"#64748b\",\n                                    padding: 24,\n                                    background: \"#fff\",\n                                    borderRadius: 14,\n                                    boxShadow: \"0 2px 16px #e0e7ef\"\n                                },\n                                children: \"No employees found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this),\n                            filteredEmployees.map((emp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#fff\",\n                                        borderRadius: 14,\n                                        boxShadow: \"0 2px 16px #e0e7ef\",\n                                        padding: \"1.5rem 1.2rem\",\n                                        display: \"flex\",\n                                        flexDirection: \"column\",\n                                        gap: 10,\n                                        transition: \"box-shadow 0.2s, transform 0.2s\",\n                                        cursor: \"pointer\",\n                                        position: \"relative\",\n                                        minHeight: 140\n                                    },\n                                    onMouseOver: (e)=>{\n                                        e.currentTarget.style.boxShadow = \"0 4px 24px #bae6fd\";\n                                        e.currentTarget.style.transform = \"translateY(-2px) scale(1.02)\";\n                                    },\n                                    onMouseOut: (e)=>{\n                                        e.currentTarget.style.boxShadow = \"0 2px 16px #e0e7ef\";\n                                        e.currentTarget.style.transform = \"none\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: 12\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Avatar, {\n                                                    name: emp.fullName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontWeight: 700,\n                                                                fontSize: 16,\n                                                                color: \"#0D9488\"\n                                                            },\n                                                            children: emp.fullName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: 13,\n                                                                color: \"#64748b\",\n                                                                fontWeight: 500\n                                                            },\n                                                            children: emp.position\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: 10,\n                                                marginTop: 4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    color: departmentColors[emp.department] || \"#64748b\",\n                                                    children: emp.department\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    color: emp.status === \"Active\" ? \"#059669\" : \"#e11d48\",\n                                                    children: emp.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: 12,\n                                                right: 12,\n                                                display: \"flex\",\n                                                gap: 6\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleView(emp),\n                                                    style: iconBtn,\n                                                    title: \"View\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaEye, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 88\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openEditModal(emp),\n                                                    style: iconBtn,\n                                                    title: \"Edit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaEdit, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 91\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDelete(emp),\n                                                    style: {\n                                                        ...iconBtn,\n                                                        color: \"#e11d48\"\n                                                    },\n                                                    title: \"Delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaTrash, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 117\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, emp.id, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                modalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: modalOverlay,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: modalBox,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: 0,\n                                    marginBottom: 18\n                                },\n                                children: editMode ? \"Edit Employee\" : \"Add Employee\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: 10,\n                                    marginBottom: 18\n                                },\n                                children: formSteps.map((step, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: 22,\n                                                    height: 22,\n                                                    borderRadius: \"50%\",\n                                                    background: idx === formStep ? \"#0D9488\" : \"#e5e7eb\",\n                                                    color: idx === formStep ? \"#fff\" : \"#64748b\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    fontWeight: 700,\n                                                    fontSize: 13,\n                                                    border: idx === formStep ? \"2px solid #0D9488\" : \"2px solid #e5e7eb\"\n                                                },\n                                                children: idx + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 21\n                                            }, this),\n                                            idx < formSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    width: 24,\n                                                    height: 2,\n                                                    background: \"#e5e7eb\",\n                                                    borderRadius: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 52\n                                            }, this)\n                                        ]\n                                    }, step, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"1fr\",\n                                    gap: 14,\n                                    minWidth: 320,\n                                    maxWidth: 420\n                                },\n                                children: [\n                                    stepFields[formStep],\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            gap: 10,\n                                            marginTop: 10\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setFormStep((s)=>Math.max(0, s - 1)),\n                                                style: {\n                                                    ...modalBtn,\n                                                    background: \"#e5e7eb\",\n                                                    color: \"#334155\",\n                                                    visibility: formStep === 0 ? \"hidden\" : \"visible\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaChevronLeft, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 210\n                                                    }, this),\n                                                    \" Back\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this),\n                                            formStep < formSteps.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setFormStep((s)=>Math.min(formSteps.length - 1, s + 1)),\n                                                style: {\n                                                    ...modalBtn,\n                                                    background: \"#0D9488\",\n                                                    color: \"#fff\"\n                                                },\n                                                children: [\n                                                    \"Next \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaChevronRight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 182\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                style: {\n                                                    ...modalBtn,\n                                                    background: \"#0D9488\",\n                                                    color: \"#fff\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaSave, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 105\n                                                    }, this),\n                                                    \" \",\n                                                    editMode ? \"Save\" : \"Add\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: closeModal,\n                                                style: {\n                                                    ...modalBtn,\n                                                    background: \"#e5e7eb\",\n                                                    color: \"#334155\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaTimes, {}, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 127\n                                                    }, this),\n                                                    \" Cancel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this),\n                viewEmployee && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: modalOverlay,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: modalBox,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: 0,\n                                    marginBottom: 18\n                                },\n                                children: \"Employee Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"1fr 1fr\",\n                                    gap: 12,\n                                    fontSize: 15\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Name:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.fullName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"DOB:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.dob\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Gender:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.gender\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Nationality:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.nationality\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Marital Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.maritalStatus\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Address:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.address\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Phone:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.phone\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"NIN:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.nin\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"BVN:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.bvn\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"TIN:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.tin\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Pension PIN:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.pensionPin\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Bank Name:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.bankName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Account Number:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.accountNumber\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Position:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.position\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Department:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.department\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Salary:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.salary\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Start Date:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.startDate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: viewEmployee.status === \"Active\" ? \"#059669\" : \"#e11d48\",\n                                                    fontWeight: 600\n                                                },\n                                                children: viewEmployee.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            gridColumn: \"1/3\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Next of Kin:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 52\n                                            }, this),\n                                            \" \",\n                                            viewEmployee.nextOfKin.name,\n                                            \" (\",\n                                            viewEmployee.nextOfKin.relationship,\n                                            \") - \",\n                                            viewEmployee.nextOfKin.phone\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"flex-end\",\n                                    marginTop: 18\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeView,\n                                    style: {\n                                        ...modalBtn,\n                                        background: \"#0D9488\",\n                                        color: \"#fff\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaTimes, {}, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 107\n                                        }, this),\n                                        \" Close\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(EMSPage, \"tHVBE6DlFKaqc5CL8gXplm50cng=\");\n_c = EMSPage;\n// UI helpers\nconst th = {\n    padding: \"10px 8px\",\n    borderBottom: \"1px solid #e5e7eb\",\n    fontWeight: 700,\n    textAlign: \"left\",\n    background: \"#f1f5f9\",\n    position: \"sticky\",\n    top: 0,\n    zIndex: 2\n};\nconst td = {\n    padding: \"12px 8px\",\n    borderBottom: \"1px solid #e5e7eb\",\n    background: \"none\",\n    verticalAlign: \"middle\"\n};\nconst iconBtn = {\n    background: \"none\",\n    border: \"none\",\n    color: \"#2563eb\",\n    fontSize: 17,\n    cursor: \"pointer\",\n    marginRight: 6,\n    padding: 4,\n    borderRadius: 4,\n    transition: \"background 0.2s\"\n};\nconst modalOverlay = {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    width: \"100vw\",\n    height: \"100vh\",\n    background: \"rgba(30,41,59,0.18)\",\n    zIndex: 1000,\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n};\nconst modalBox = {\n    background: \"#fff\",\n    borderRadius: 12,\n    padding: 32,\n    minWidth: 420,\n    maxWidth: 600,\n    boxShadow: \"0 4px 32px #cbd5e1\",\n    position: \"relative\"\n};\nconst modalBtn = {\n    border: \"none\",\n    borderRadius: 6,\n    padding: \"8px 18px\",\n    fontWeight: 600,\n    fontSize: 15,\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: 6,\n    cursor: \"pointer\"\n};\nfunction Input(param) {\n    let { label , ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 2\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                style: {\n                    fontWeight: 500,\n                    marginBottom: 2\n                },\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ...props,\n                style: {\n                    ...props.style,\n                    padding: 7,\n                    borderRadius: 5,\n                    border: \"1px solid #cbd5e1\",\n                    fontSize: 15\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Input;\nfunction Select(param) {\n    let { label , options , ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 2\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                style: {\n                    fontWeight: 500,\n                    marginBottom: 2\n                },\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                ...props,\n                style: {\n                    ...props.style,\n                    padding: 7,\n                    borderRadius: 5,\n                    border: \"1px solid #cbd5e1\",\n                    fontSize: 15\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                        value: \"\",\n                        children: \"Select\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    options.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: opt,\n                            children: opt\n                        }, opt, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 29\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_c2 = Select;\nfunction Badge(param) {\n    let { color , children  } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: {\n            background: color,\n            color: \"#fff\",\n            borderRadius: 8,\n            padding: \"2px 10px\",\n            fontWeight: 600,\n            fontSize: 13\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 340,\n        columnNumber: 10\n    }, this);\n}\n_c3 = Badge;\nfunction Avatar(param) {\n    let { name  } = param;\n    const initials = name.split(\" \").map((n)=>n[0]).join(\"\").toUpperCase().slice(0, 2);\n    const bg = \"#0D9488\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: {\n            width: 34,\n            height: 34,\n            borderRadius: \"50%\",\n            background: bg,\n            color: \"#fff\",\n            display: \"inline-flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            fontWeight: 700,\n            fontSize: 16,\n            boxShadow: \"0 1px 4px #cbd5e1\"\n        },\n        children: initials\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_c4 = Avatar;\n// EmployeeRoles component for role management\nfunction EmployeeRoles(param) {\n    let { userId  } = param;\n    _s1();\n    const [roles, setRoles] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [userRoles, setUserRoles] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [assigning, setAssigning] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const [selectedRole, setSelectedRole] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        let cancelled = false;\n        setLoading(true);\n        setError(null);\n        Promise.all([\n            fetch(\"http://localhost:4001/roles\").then((r)=>r.ok ? r.json() : []),\n            fetch(\"http://localhost:4001/user-roles/\".concat(userId)).then((r)=>r.ok ? r.json() : [])\n        ]).then((param)=>{\n            let [rolesData, userRolesData] = param;\n            if (!cancelled) {\n                setRoles(Array.isArray(rolesData) ? rolesData : []);\n                setUserRoles(Array.isArray(userRolesData) ? userRolesData : []);\n                setLoading(false);\n            }\n        }).catch((e)=>{\n            if (!cancelled) {\n                setError(\"Failed to load roles\");\n                setLoading(false);\n            }\n        });\n        return ()=>{\n            cancelled = true;\n        };\n    }, [\n        userId,\n        assigning\n    ]);\n    const handleAssign = async ()=>{\n        if (!selectedRole) return;\n        setAssigning(true);\n        try {\n            await fetch(\"http://localhost:4001/assign-role\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    user_id: userId,\n                    role_id: selectedRole,\n                    duration_hours: 72,\n                    context: {}\n                })\n            });\n        } catch (e) {\n            setError(\"Failed to assign role\");\n        }\n        setAssigning(false);\n        setSelectedRole(\"\");\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: \"Loading...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 399,\n        columnNumber: 23\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: {\n            color: \"red\",\n            fontSize: 13\n        },\n        children: error\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 400,\n        columnNumber: 21\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 4\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    flexWrap: \"wrap\",\n                    gap: 4\n                },\n                children: [\n                    (!userRoles || userRoles.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            color: \"#64748b\",\n                            fontSize: 13\n                        },\n                        children: \"No roles\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 52\n                    }, this),\n                    userRoles && userRoles.map((ur)=>{\n                        const role = roles.find((r)=>r.id === ur.role_id);\n                        return role ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                            color: \"#2563eb\",\n                            children: role.name\n                        }, ur.role_id, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 25\n                        }, this) : null;\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedRole,\n                        onChange: (e)=>setSelectedRole(e.target.value),\n                        style: {\n                            fontSize: 13,\n                            padding: 3,\n                            borderRadius: 4,\n                            border: \"1px solid #cbd5e1\",\n                            marginRight: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Assign role...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            roles.filter((r)=>!userRoles.some((ur)=>ur.role_id === r.id)).map((r)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: r.id,\n                                    children: r.name\n                                }, r.id, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleAssign,\n                        disabled: !selectedRole || assigning,\n                        style: {\n                            fontSize: 13,\n                            padding: \"3px 10px\",\n                            borderRadius: 4,\n                            background: \"#0D9488\",\n                            color: \"#fff\",\n                            border: \"none\",\n                            fontWeight: 600,\n                            cursor: \"pointer\"\n                        },\n                        children: \"Assign\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\app\\\\ems\\\\page.tsx\",\n        lineNumber: 402,\n        columnNumber: 5\n    }, this);\n}\n_s1(EmployeeRoles, \"TsVZe8tla10PDxmTr0Bhz50O5T4=\");\n_c5 = EmployeeRoles;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"EMSPage\");\n$RefreshReg$(_c1, \"Input\");\n$RefreshReg$(_c2, \"Select\");\n$RefreshReg$(_c3, \"Badge\");\n$RefreshReg$(_c4, \"Avatar\");\n$RefreshReg$(_c5, \"EmployeeRoles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/ems/page.tsx\n"));

/***/ })

});