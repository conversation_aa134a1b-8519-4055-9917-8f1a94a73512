-- Enhanced EMS Database Schema
-- This file contains additional tables for comprehensive employee management

-- Employee addresses with encryption
CREATE TABLE IF NOT EXISTS employee_addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    address_type address_type_enum,
    street_address_encrypted BYTEA,
    city_encrypted BYTEA,
    state_province VARCHAR(50),
    postal_code VARCHAR(20),
    country_code CHAR(2),
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee documents
CREATE TABLE IF NOT EXISTS employee_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    document_type document_type_enum,
    document_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    is_confidential BO<PERSON>EAN DEFAULT false,
    uploaded_by UUID REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    metadata JSONB
);

-- Employee onboarding workflow
CREATE TABLE IF NOT EXISTS employee_onboarding (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    status onboarding_status_enum DEFAULT 'pending',
    assigned_buddy UUID REFERENCES employees(id),
    start_date DATE,
    expected_completion_date DATE,
    actual_completion_date DATE,
    checklist JSONB,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee performance reviews
CREATE TABLE IF NOT EXISTS performance_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES employees(id),
    review_period_start DATE,
    review_period_end DATE,
    overall_rating DECIMAL(3,2),
    goals_achievement DECIMAL(3,2),
    competencies JSONB,
    feedback TEXT,
    development_plan TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    submitted_at TIMESTAMP,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee skills and certifications
CREATE TABLE IF NOT EXISTS employee_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    skill_name VARCHAR(100) NOT NULL,
    proficiency_level INTEGER CHECK (proficiency_level >= 1 AND proficiency_level <= 5),
    years_of_experience DECIMAL(3,1),
    is_certified BOOLEAN DEFAULT false,
    certification_name VARCHAR(200),
    certification_date DATE,
    expiry_date DATE,
    issuing_authority VARCHAR(200),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee training records
CREATE TABLE IF NOT EXISTS employee_training (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    training_name VARCHAR(200) NOT NULL,
    training_type VARCHAR(50),
    provider VARCHAR(200),
    start_date DATE,
    completion_date DATE,
    status VARCHAR(20) DEFAULT 'enrolled',
    score DECIMAL(5,2),
    certificate_url VARCHAR(500),
    cost DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee time tracking
CREATE TABLE IF NOT EXISTS employee_time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    clock_in TIMESTAMP,
    clock_out TIMESTAMP,
    break_duration INTEGER DEFAULT 0, -- in minutes
    total_hours DECIMAL(4,2),
    overtime_hours DECIMAL(4,2) DEFAULT 0,
    location VARCHAR(100),
    notes TEXT,
    approved_by UUID REFERENCES employees(id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee leave requests
CREATE TABLE IF NOT EXISTS employee_leave_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    leave_type VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_days INTEGER NOT NULL,
    reason TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    approved_by UUID REFERENCES employees(id),
    approved_at TIMESTAMP,
    rejected_reason TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee equipment assignments
CREATE TABLE IF NOT EXISTS employee_equipment (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    equipment_type VARCHAR(50) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    asset_tag VARCHAR(50),
    assigned_date DATE NOT NULL,
    return_date DATE,
    condition VARCHAR(20) DEFAULT 'good',
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Audit logs for employee data changes
CREATE TABLE IF NOT EXISTS employee_audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id),
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by UUID REFERENCES users(id),
    changed_at TIMESTAMP DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- AI predictions for employees
CREATE TABLE IF NOT EXISTS employee_ai_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    prediction_type VARCHAR(50) NOT NULL, -- 'attrition_risk', 'performance_score', 'promotion_readiness'
    prediction_value DECIMAL(5,4), -- Probability score 0-1
    confidence_score DECIMAL(5,4),
    model_name VARCHAR(100),
    model_version VARCHAR(20),
    features_used JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_employees_department_id ON employees(department_id);
CREATE INDEX IF NOT EXISTS idx_employees_manager_id ON employees(manager_id);
CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(status);
CREATE INDEX IF NOT EXISTS idx_employees_hire_date ON employees(hire_date);
CREATE INDEX IF NOT EXISTS idx_employee_addresses_employee_id ON employee_addresses(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_documents_employee_id ON employee_documents(employee_id);
CREATE INDEX IF NOT EXISTS idx_performance_reviews_employee_id ON performance_reviews(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_skills_employee_id ON employee_skills(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_training_employee_id ON employee_training(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_time_entries_employee_id ON employee_time_entries(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_time_entries_date ON employee_time_entries(date);
CREATE INDEX IF NOT EXISTS idx_employee_leave_requests_employee_id ON employee_leave_requests(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_equipment_employee_id ON employee_equipment(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_audit_logs_employee_id ON employee_audit_logs(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_ai_predictions_employee_id ON employee_ai_predictions(employee_id);

-- Full-text search indexes
CREATE INDEX IF NOT EXISTS idx_employees_search ON employees USING gin(to_tsvector('english', job_title));
CREATE INDEX IF NOT EXISTS idx_employee_skills_search ON employee_skills USING gin(to_tsvector('english', skill_name));
