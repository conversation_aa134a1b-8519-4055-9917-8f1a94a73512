# PowerShell script to set up local PostgreSQL user and database for PeopleNest HRMS
# Prompts for your local postgres superuser password

$PGUSER = "postgres"
$PGPASSWORD = Read-Host -Prompt "Enter your postgres superuser password"
$env:PGPASSWORD = $PGPASSWORD

# Create user 'user' if not exists
psql -U $PGUSER -h localhost -c "DO $$ BEGIN IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'user') THEN CREATE USER \"user\" WITH PASSWORD 'password'; END IF; END $$;"

# Create database 'peoplenest' if not exists, owned by 'user'
psql -U $PGUSER -h localhost -c "DO $$ BEGIN IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'peoplenest') THEN CREATE DATABASE peoplenest OWNER \"user\"; END IF; END $$;"

Write-Host "User 'postgres' and database 'peoplenest' are ready."
