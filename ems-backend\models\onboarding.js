import pool from '../db.js';

// Default onboarding checklist
const DEFAULT_ONBOARDING_CHECKLIST = [
  {
    id: 1,
    task: 'Complete employment paperwork',
    description: 'Fill out tax forms, emergency contacts, and other required documents',
    category: 'documentation',
    required: true,
    estimatedDays: 1,
    completed: false,
    completedAt: null,
    assignedTo: 'hr'
  },
  {
    id: 2,
    task: 'IT setup and equipment assignment',
    description: 'Receive laptop, phone, access cards, and software accounts',
    category: 'equipment',
    required: true,
    estimatedDays: 1,
    completed: false,
    completedAt: null,
    assignedTo: 'it'
  },
  {
    id: 3,
    task: 'Office tour and workspace assignment',
    description: 'Tour of office facilities, parking, cafeteria, and workspace setup',
    category: 'orientation',
    required: true,
    estimatedDays: 1,
    completed: false,
    completedAt: null,
    assignedTo: 'hr'
  },
  {
    id: 4,
    task: 'Meet team members and key stakeholders',
    description: 'Introduction to immediate team, manager, and key collaborators',
    category: 'social',
    required: true,
    estimatedDays: 2,
    completed: false,
    completedAt: null,
    assignedTo: 'manager'
  },
  {
    id: 5,
    task: 'Review job description and expectations',
    description: 'Detailed discussion of role, responsibilities, and performance expectations',
    category: 'role_clarity',
    required: true,
    estimatedDays: 1,
    completed: false,
    completedAt: null,
    assignedTo: 'manager'
  },
  {
    id: 6,
    task: 'Complete mandatory training modules',
    description: 'Safety training, compliance training, and company policies',
    category: 'training',
    required: true,
    estimatedDays: 3,
    completed: false,
    completedAt: null,
    assignedTo: 'hr'
  },
  {
    id: 7,
    task: 'Set up initial goals and objectives',
    description: 'Establish 30-60-90 day goals and key performance indicators',
    category: 'goal_setting',
    required: true,
    estimatedDays: 1,
    completed: false,
    completedAt: null,
    assignedTo: 'manager'
  },
  {
    id: 8,
    task: 'Schedule regular check-ins',
    description: 'Set up weekly one-on-ones and feedback sessions for first month',
    category: 'support',
    required: true,
    estimatedDays: 1,
    completed: false,
    completedAt: null,
    assignedTo: 'manager'
  }
];

// Create onboarding workflow for new employee
export async function createOnboardingWorkflow(employeeId, customChecklist = null) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Get employee details
    const employeeQuery = await client.query(
      'SELECT hire_date, manager_id FROM employees WHERE id = $1',
      [employeeId]
    );

    if (employeeQuery.rows.length === 0) {
      throw new Error('Employee not found');
    }

    const employee = employeeQuery.rows[0];
    const hireDate = new Date(employee.hire_date);
    const expectedCompletionDate = new Date(hireDate.getTime() + 14 * 24 * 60 * 60 * 1000); // 14 days

    // Create onboarding record
    const onboardingResult = await client.query(`
      INSERT INTO employee_onboarding (
        employee_id, status, start_date, expected_completion_date, 
        assigned_buddy, checklist
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      employeeId,
      'pending',
      hireDate,
      expectedCompletionDate,
      employee.manager_id, // Assign manager as buddy by default
      JSON.stringify(customChecklist || DEFAULT_ONBOARDING_CHECKLIST)
    ]);

    await client.query('COMMIT');
    return onboardingResult.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Get onboarding workflow by employee ID
export async function getOnboardingByEmployeeId(employeeId) {
  const query = `
    SELECT 
      eo.*,
      e.employee_id as emp_number,
      e.hire_date,
      buddy.employee_id as buddy_employee_id
    FROM employee_onboarding eo
    JOIN employees e ON eo.employee_id = e.id
    LEFT JOIN employees buddy ON eo.assigned_buddy = buddy.id
    WHERE eo.employee_id = $1
    ORDER BY eo.created_at DESC
    LIMIT 1
  `;
  
  const result = await pool.query(query, [employeeId]);
  return result.rows.length > 0 ? result.rows[0] : null;
}

// Update onboarding checklist item
export async function updateOnboardingTask(onboardingId, taskId, updateData) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Get current onboarding record
    const onboardingQuery = await client.query(
      'SELECT checklist FROM employee_onboarding WHERE id = $1',
      [onboardingId]
    );

    if (onboardingQuery.rows.length === 0) {
      throw new Error('Onboarding record not found');
    }

    const checklist = onboardingQuery.rows[0].checklist;
    const taskIndex = checklist.findIndex(task => task.id === taskId);

    if (taskIndex === -1) {
      throw new Error('Task not found in checklist');
    }

    // Update the task
    checklist[taskIndex] = {
      ...checklist[taskIndex],
      ...updateData,
      completedAt: updateData.completed ? new Date() : null
    };

    // Check if all required tasks are completed
    const allRequiredCompleted = checklist
      .filter(task => task.required)
      .every(task => task.completed);

    const newStatus = allRequiredCompleted ? 'completed' : 'in_progress';
    const actualCompletionDate = allRequiredCompleted ? new Date() : null;

    // Update onboarding record
    const updateResult = await client.query(`
      UPDATE employee_onboarding 
      SET checklist = $1, status = $2, actual_completion_date = $3, updated_at = NOW()
      WHERE id = $4
      RETURNING *
    `, [JSON.stringify(checklist), newStatus, actualCompletionDate, onboardingId]);

    await client.query('COMMIT');
    return updateResult.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Get all onboarding workflows with filtering
export async function getAllOnboardingWorkflows(options = {}) {
  const { 
    page = 1, 
    limit = 50, 
    status = '', 
    startDate = '', 
    endDate = '',
    sortBy = 'created_at',
    sortOrder = 'DESC' 
  } = options;

  const offset = (page - 1) * limit;
  let whereClause = 'WHERE 1=1';
  const queryParams = [];
  let paramCount = 0;

  if (status) {
    paramCount++;
    whereClause += ` AND eo.status = $${paramCount}`;
    queryParams.push(status);
  }

  if (startDate) {
    paramCount++;
    whereClause += ` AND eo.start_date >= $${paramCount}`;
    queryParams.push(startDate);
  }

  if (endDate) {
    paramCount++;
    whereClause += ` AND eo.start_date <= $${paramCount}`;
    queryParams.push(endDate);
  }

  const query = `
    SELECT 
      eo.*,
      e.employee_id as emp_number,
      e.job_title,
      d.name as department_name,
      buddy.employee_id as buddy_employee_id,
      COUNT(*) OVER() as total_count
    FROM employee_onboarding eo
    JOIN employees e ON eo.employee_id = e.id
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN employees buddy ON eo.assigned_buddy = buddy.id
    ${whereClause}
    ORDER BY eo.${sortBy} ${sortOrder}
    LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
  `;

  queryParams.push(limit, offset);
  const result = await pool.query(query, queryParams);
  
  return {
    onboardingWorkflows: result.rows,
    totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
    page,
    limit,
    totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0
  };
}

// Get onboarding statistics
export async function getOnboardingStatistics() {
  const query = `
    SELECT 
      COUNT(*) as total_onboarding,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_onboarding,
      COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_onboarding,
      COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_onboarding,
      COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_onboarding,
      AVG(CASE 
        WHEN actual_completion_date IS NOT NULL 
        THEN EXTRACT(days FROM actual_completion_date - start_date)
        ELSE NULL 
      END) as average_completion_days,
      COUNT(CASE 
        WHEN actual_completion_date IS NOT NULL 
        AND actual_completion_date <= expected_completion_date 
        THEN 1 
      END) as on_time_completions
    FROM employee_onboarding
    WHERE created_at >= CURRENT_DATE - INTERVAL '1 year'
  `;
  
  const result = await pool.query(query);
  return result.rows[0];
}

// Update onboarding status
export async function updateOnboardingStatus(onboardingId, status, notes = null) {
  const result = await pool.query(`
    UPDATE employee_onboarding 
    SET status = $1, notes = COALESCE($2, notes), updated_at = NOW()
    WHERE id = $3
    RETURNING *
  `, [status, notes, onboardingId]);
  
  if (result.rows.length === 0) {
    throw new Error('Onboarding record not found');
  }
  
  return result.rows[0];
}

// Assign buddy to onboarding
export async function assignOnboardingBuddy(onboardingId, buddyId) {
  const result = await pool.query(`
    UPDATE employee_onboarding 
    SET assigned_buddy = $1, updated_at = NOW()
    WHERE id = $2
    RETURNING *
  `, [buddyId, onboardingId]);
  
  if (result.rows.length === 0) {
    throw new Error('Onboarding record not found');
  }
  
  return result.rows[0];
}
