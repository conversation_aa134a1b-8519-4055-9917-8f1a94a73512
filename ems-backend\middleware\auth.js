import jwt from 'jsonwebtoken';
import pool from '../db.js';

// JWT secret key (should be in environment variables)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Middleware to authenticate JWT tokens
export function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ 
      error: 'Access token required',
      message: 'Please provide a valid access token'
    });
  }

  jwt.verify(token, JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(403).json({ 
        error: 'Invalid token',
        message: 'The provided token is invalid or expired'
      });
    }

    try {
      // Get user details from database
      const userQuery = `
        SELECT u.id, u.email, u.role, u.is_active,
               e.id as employee_id, e.employee_id as emp_number
        FROM users u
        LEFT JOIN employees e ON u.id = e.user_id
        WHERE u.id = $1 AND u.is_active = true
      `;
      
      const userResult = await pool.query(userQuery, [decoded.userId]);
      
      if (userResult.rows.length === 0) {
        return res.status(403).json({ 
          error: 'User not found or inactive',
          message: 'The user associated with this token is not found or inactive'
        });
      }

      req.user = userResult.rows[0];
      next();
    } catch (error) {
      console.error('Error in authentication middleware:', error);
      return res.status(500).json({ 
        error: 'Authentication error',
        message: 'An error occurred during authentication'
      });
    }
  });
}

// Middleware to check if user is admin
export function requireAdmin(req, res, next) {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ 
      error: 'Admin access required',
      message: 'This operation requires administrator privileges'
    });
  }
  next();
}

// Middleware to check if user is HR or admin
export function requireHROrAdmin(req, res, next) {
  if (!req.user || !['hr', 'admin'].includes(req.user.role)) {
    return res.status(403).json({ 
      error: 'HR or Admin access required',
      message: 'This operation requires HR or administrator privileges'
    });
  }
  next();
}

// Middleware to check if user can access employee data
export function canAccessEmployee(req, res, next) {
  const employeeId = req.params.id;
  
  // Admin and HR can access any employee
  if (['admin', 'hr'].includes(req.user.role)) {
    return next();
  }
  
  // Managers can access their direct reports
  if (req.user.role === 'manager') {
    // This would require additional logic to check if the employee reports to this manager
    // For now, we'll allow it and implement the check in the controller
    return next();
  }
  
  // Employees can only access their own data
  if (req.user.employee_id === employeeId) {
    return next();
  }
  
  return res.status(403).json({ 
    error: 'Access denied',
    message: 'You do not have permission to access this employee data'
  });
}

// Generate JWT token
export function generateToken(userId, email, role) {
  const payload = {
    userId,
    email,
    role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  return jwt.sign(payload, JWT_SECRET);
}

// Generate refresh token
export function generateRefreshToken(userId) {
  const payload = {
    userId,
    type: 'refresh',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  };
  
  return jwt.sign(payload, JWT_SECRET);
}

// Verify refresh token
export function verifyRefreshToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }
    return decoded;
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
}

// Optional middleware for routes that work with or without authentication
export function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    req.user = null;
    return next();
  }

  jwt.verify(token, JWT_SECRET, async (err, decoded) => {
    if (err) {
      req.user = null;
      return next();
    }

    try {
      const userQuery = `
        SELECT u.id, u.email, u.role, u.is_active,
               e.id as employee_id, e.employee_id as emp_number
        FROM users u
        LEFT JOIN employees e ON u.id = e.user_id
        WHERE u.id = $1 AND u.is_active = true
      `;
      
      const userResult = await pool.query(userQuery, [decoded.userId]);
      
      if (userResult.rows.length > 0) {
        req.user = userResult.rows[0];
      } else {
        req.user = null;
      }
      
      next();
    } catch (error) {
      console.error('Error in optional auth middleware:', error);
      req.user = null;
      next();
    }
  });
}

// Rate limiting middleware (basic implementation)
const rateLimitMap = new Map();

export function rateLimit(maxRequests = 100, windowMs = 15 * 60 * 1000) {
  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!rateLimitMap.has(key)) {
      rateLimitMap.set(key, []);
    }
    
    const requests = rateLimitMap.get(key);
    
    // Remove old requests outside the window
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    
    if (validRequests.length >= maxRequests) {
      return res.status(429).json({
        error: 'Too many requests',
        message: `Rate limit exceeded. Maximum ${maxRequests} requests per ${windowMs / 1000} seconds.`,
        retryAfter: Math.ceil((validRequests[0] + windowMs - now) / 1000)
      });
    }
    
    validRequests.push(now);
    rateLimitMap.set(key, validRequests);
    
    next();
  };
}
